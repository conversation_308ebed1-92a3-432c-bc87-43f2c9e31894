<template>
  <div>
    <pdf-preview :fileUrl='pdfUrl' />
  </div>
</template>

<script>
import pdfPreview from "@/components/pdfPreview/index.vue";

export default {
  components: { pdfPreview},
  data(){
    return{
      pdfUrl:'',
    }
  },
  created(){
    this.handleParams()
  },
  methods:{
    handleParams(){
      this.pdfUrl = decodeURIComponent(this.$route.query.pdfUrl)
      if (this.$route.query.title) {
        document.title = decodeURIComponent(this.$route.query.pdfUrl)
      }
    },
  }
}
</script>

<style>

</style>