<template>
  <div class="main-box">
    <div class="newMain" :class="{ active: isIphoneX }">
      <inviteTop :inviteId="inviteId" />

      <van-tabs type="card" :background="'#FCE2CD'" sticky>
        <van-tab v-if="isLogin && isPay" title="奖学金" name="recruit">
          <!-- <van-tab title="奖学金" name="recruit"> -->
          <div class="scholarship">
            <!-- 激活 -->
            <div class="bonus" v-if="runData.challengeStatus != 3">
              <div class="top">
                <span>待激活</span>
                <!-- todo -->
                <img src="https://static.yzou.cn/zmc/a20240704ChengJian/no-activated-money.png" alt />
                <!-- <span class="money">千元奖学金</span> -->
                <span>奖学金</span>
              </div>
              <div class="bottom" @click="ruleShow = true">
                详细规则
                <van-icon name="arrow" color="#8C4D0E" />
              </div>
            </div>
            <!-- 未激活 -->
            <div class="inactivated-box" v-else>
              <div class="txt-box">
                <span>激活成功</span>
                <div class="bottom" @click="ruleShow = true">
                  详细规则
                  <van-icon name="arrow" color="#8C4D0E" />
                </div>
              </div>
            </div>
            <div class="invite-group">
              <div class="lf">
                <img
                  src="../../../assets/image/active/signAct/group-img.png"
                  alt
                />
                <p>请同学务必加入奖学金班级群，与更多伙伴一起打卡吧</p>
              </div>
              <div class="rg">
                <button @click="codeShow = true">点击加群</button>
              </div>
            </div>
            <div class="tips-box">
              <h3>完成2个挑战，激活奖学金</h3>
              <ul>
                <li>
                  <p>
                    若
                    <strong>「挑战一」</strong> 失败，则两项挑战均须
                    <strong>重新开始，跑步打卡</strong>次数
                    <strong>重新累计</strong> 。
                  </p>
                </li>
                <li>
                  <p>
                    若挑战期内，
                    <strong>「挑战一」</strong>成功、
                    <strong>「挑战二」</strong> 失败，则复训时只需要完成
                    <strong>「挑战二」</strong> ；
                  </p>
                </li>
                <li>
                  <p>
                    所有学员均有2次复训机会，若
                    <strong>3轮挑战均失败，</strong>则
                    <strong>无法激活</strong>奖学金；
                  </p>
                </li>
                <li>
                  <p>
                    最迟挑战开启时间为
                    <strong style="text-decoration: underline"
                      >2025.06.13</strong
                    >
                    ，逾期则
                    <strong style="text-decoration: underline">无法激活</strong
                    >奖学金；
                  </p>
                </li>
              </ul>
            </div>
            <!-- 学习打卡 -->
            <StudyBonus
              :semesterData="semesterData"
              :studyData="studyData"
              :runData="runData"
              :newCard="newCard"
              :patchCard="patchCard"
              :mobile="mobile"
            ></StudyBonus>
            <!-- 跑步打卡 -->
            <RunBonus
              :semesterData="semesterData"
              :studyData="studyData"
              :runData="runData"
              :mobile="mobile"
            >
            </RunBonus>
            <!-- 排行榜 -->
            <LeaderBoard
              v-if="studyData.challengeStatus == 2"
              :rankingList="rankingList"
              :myData="myData"
              @handelLoad="loadMore"
              :showNoMore="showNoMore"
            ></LeaderBoard>
          </div>
        </van-tab>
        <van-tab title="报读主页" name="sprint">
          <div
            class="headBanner"
            :class="{ activeTime: isShowEnd == 1 }"
            @click.self="nowGB"
          >
            <div v-if="isScrollEnrollList" class="textContentJoin">
              <div class="gdBox" :class="{ anim: animate == true }">
                <p v-for="(item, index) in items" :key="index">
                  <img :src="item.headImg | defaultAvatar" />
                  <span class="name">{{ item.realName | hideName }}</span>
                  <span>已报读，完成挑战并激活奖学金</span>
                </p>
              </div>
            </div>
          </div>
          <div class="bonus">
            <div class="bonus-child">
              <div class="ticket">
                <!-- todo -->
                <img src="https://static.yzou.cn/zmc/a20240704ChengJian/coupons.png" alt />
              </div>
              <div class="line"></div>
              <div class="ticket_detail">
                <div class="ul">
                  <div>附赠:</div>
                  <div class="li">
                    <img
                      src="../../../assets/image/active/signAct/gift.png"
                      alt
                    />
                    <span>考前辅导课</span>
                  </div>
                  <div class="li">
                    <img
                      src="../../../assets/image/active/signAct/gift.png"
                      alt
                    />
                    <span>三本辅导教材</span>
                  </div>
                </div>
                <!-- <div class="baoButton" @click="nowGB">前往报名</div> -->
                <img
                  class="applic-btn"
                  @click="nowGB"
                  src="./img/applic-btn.png"
                  alt
                />
                <img
                  class="tips-img"
                  src="../../../assets/image/active/signAct/tips.png"
                  alt
                />
              </div>
            </div>
            <div class="proGress"></div>
          </div>
          <img
            class="title-img"
            src="../../../assets/image/active/signAct/act-title.png"
            alt
            srcset
          />
          <div class="proGress">
            <img src="../../../assets/image/active/signAct/progres.png" alt />
          </div>
          <div class="tip-box">
            <span class="text"
              >奖学金需要完成“21天学习”和“21次跑步”挑战才可激活</span
            >
          </div>
          <div class="tip-box">
            <span class="text"
              >挑战成功后，奖学金券将发放到学员账户，用于抵扣学费，详细使用条件见“规则介绍”</span
            >
          </div>
          <img
            class="title-img"
            src="../../../assets/image/active/signAct/school-title.png"
            alt
            srcset
          />
          <div class="schoolnum">
            <div class="school-detail">
              <div class="school-pic"></div>
              <img src="https://static.yzou.cn/zmc/a20240704ChengJian/cjxy.png" alt />
              <div class="oneschoolName">广州城建职业学院</div>
            </div>
          </div>
          <img
            class="title-img charge"
            src="../../../assets/image/active/signAct/charge-title.png"
            alt
            srcset
          />
          <div class="chargetop">
            <img
              src="../../../assets/image/active/enrollmentHomepage/charge.png"
              alt
            />
            <p>院校专业及收费</p>
          </div>
          <div class="schoolnum">
            <div class="info" id="ln">
              <div class="table dsnm">
                <div class="table-b">
                  <table>
                    <thead>
                      <tr class="title_calss">
                        <th colspan="4">理工类（高起专）</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="text_name title_text">招生专业</td>
                        <td class="text_study title_text">学费</td>
                        <td class="text_book title_text">书杂费</td>
                        <td class="text_system title_text">学制</td>
                      </tr>
                      <tr>
                        <td class="text_name">
                          <p>工程造价</p>
                          <p>机电一体化技术</p>
                          <p>建筑工程技术</p>
                          <p>计算机应用技术</p>
                        </td>
                        <td class="text_study">3600.00元/学年</td>
                        <td class="text_book">400.00元/学年</td>
                        <td class="text_system">2.5年</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="table dsnm">
                <div class="table-b">
                  <table>
                    <thead>
                      <tr class="title_calss">
                        <th colspan="4">外语类（高起专）</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="text_name title_text">招生专业</td>
                        <td class="text_study title_text">学费</td>
                        <td class="text_book title_text">书杂费</td>
                        <td class="text_system title_text">学制</td>
                      </tr>
                      <tr>
                        <td class="text_name">
                          <p>商务英语</p>
                        </td>
                        <td class="text_study">3000.00元/学年</td>
                        <td class="text_book">400.00元/学年</td>
                        <td class="text_system">3年</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="table dsnm">
                <div class="table-b">
                  <table>
                    <thead>
                      <tr class="title_calss">
                        <th colspan="4">文史类（高起专）</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="text_name title_text">招生专业</td>
                        <td class="text_study title_text">学费</td>
                        <td class="text_book title_text">书杂费</td>
                        <td class="text_system title_text">学制</td>
                      </tr>
                      <tr>
                        <td class="text_name">
                          <p>工商企业管理</p>
                          <p>大数据与会计</p>
                          <p>电子商务</p>
                        </td>
                        <td class="text_study">3600.00元/学年</td>
                        <td class="text_book">400.00元/学年</td>
                        <td class="text_system">2.5年</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              <div class="table dsnm">
                <div class="table-b">
                  <table>
                    <thead>
                      <tr class="title_calss">
                        <th colspan="4">艺术类（高起专）</th>
                      </tr>
                    </thead>
                    <tbody>
                      <tr>
                        <td class="text_name title_text">招生专业</td>
                        <td class="text_study title_text">学费</td>
                        <td class="text_book title_text">书杂费</td>
                        <td class="text_system title_text">学制</td>
                      </tr>
                      <tr>
                        <td class="text_name">
                          <p>室内艺术设计</p>
                        </td>
                        <td class="text_study">3600.00元/学年</td>
                        <td class="text_book">400.00元/学年</td>
                        <td class="text_system">2.5年</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </div>
        </van-tab>
        <van-tab title="规则介绍" name="introduce">
          <Rule />
        </van-tab>
      </van-tabs>

      <div class="foot-top">
        <p class="foot-title">承办单位：广州远智教育科技有限公司</p>
        <p class="foot-title">邮编：51600粤ICP备12034252号-1</p>
      </div>

      <share
        :title="title"
        :desc="desc"
        :link="shareLink"
        :scholarship="scholarship"
        :imgUrl="imgUrlL"
        :regOrigin="62"
        :isActivity="true"
        ref="share"
      />
    </div>
    <second-footer v-if="!isPay" @enroll="enroll" gitBtnText="邀请有礼" />
    <!-- 选择新一轮挑战 -->
    <van-popup v-model="showNewAct" :close-on-click-overlay="false">
      <div class="choose-box">
        <img
          class="lose-img"
          src="../../../assets/image/active/signAct/lose.png"
          alt
        />
        <div class="title">
          <h3>很遗憾，您的上一轮挑战失败</h3>
          <h3>请选择新的挑战时间</h3>
          <!-- todo -->
          <p>
            *当前剩{{
              runData.remainChallengeCount
            }}次挑战机会截止在2025-08-30日前可用
          </p>
        </div>
        <div class="list-content">
          <div
            class="list-item"
            v-for="(item, index) in recordList"
            :key="index"
          >
            <div class="item-txt">
              {{ index + 1 }}.打卡开始时间：{{
                item.attendTime | formatDate("yyyy.MM.dd")
              }}
            </div>
            <button @click="showConfirmBox(item.semesterId, item.attendTime)">
              选这期
            </button>
          </div>
        </div>
      </div>
    </van-popup>
    <!-- 确认选择哪个挑战 -->
    <van-popup round v-model="confirmShow">
      <div class="confirm-box">
        <p>确定选择下一次挑战开始时间：</p>
        <p class="time">{{ newTime | formatDate("yyyy.MM.dd") }}</p>
        <div class="btn-box">
          <button class="cancel" @click="confirmShow = false">返回</button>
          <button class="confirm" @click="addNewStudy()">确定</button>
        </div>
      </div>
    </van-popup>
    <!-- 领取优惠卷弹框 -->
    <van-popup round v-model="activationShow" :close-on-click-overlay="false">
      <div class="activation-box">
        <h3>恭喜您！获得激活奖学金的资格</h3>
        <div class="img-box">
          <div class="flag">未激活</div>
          <!-- todo -->
          <img src="https://static.yzou.cn/zmc/a20240704ChengJian/coupons.png" alt />
        </div>
        <p>
          注意：本奖学金券需要完成2个挑战项来激活，否则挑战失败将自动作废奖学金。
        </p>
        <button @click="getCoupon()">点击立即参与</button>
      </div>
    </van-popup>
    <!-- 规则说明弹框 -->
    <van-popup round v-model="ruleShow">
      <div class="rule-box">
        <!-- todo -->
        <h3>奖学金规则</h3>
        <p>
          一、在2024年6月13日10点-2024年8月31日23点59分59秒期间成功报读2025级广州城建职业学院的学员（以缴纳599元购买学习大礼包为准），可额外获得5000元奖学金抵扣券。
        </p>
        <p style="margin-top: 10px">二、奖学金发放说明：</p>
        <p style="margin-top: 10px">（1）限购买599元上进礼包的学员参与。</p>
        <p>
          （2）在自报读之日起3个月内，完成累计21天的跑步打卡以及参与远智21天上进训练营后，方可激活奖学金。
        </p>
        <p>（3）仅限抵减成教2025级广州城建职业学院第2年、第2.5年或第3年等值学费。</p>
      </div>
      <van-icon
        @click="ruleShow = false"
        style="
          position: absolute;
          bottom: -50px;
          left: 50%;
          transform: translateX(-50%);
        "
        name="close"
        size="40"
        color="#ffffff"
      />
    </van-popup>
    <!-- 加微信弹框 -->
    <van-popup :close-on-click-overlay="false" round v-model="codeShow">
      <div class="qrcode-box">
        <p>请同学务必扫码添加老师微信号</p>
        <p>进入奖学金班级群</p>
        <img :src="qrCodeUrl" alt />
        <p class="code-txt">长按二维码识别</p>
      </div>
      <van-icon
        @click="codeShow = false"
        style="
          position: absolute;
          bottom: -50px;
          left: 50%;
          transform: translateX(-50%);
        "
        name="close"
        size="40"
        color="#ffffff"
      />
    </van-popup>
    <!-- 双打卡完成后弹框 -->
    <van-popup
      round
      v-model="signShow"
      @click-overlay="saveClickOpt('allStatus')"
    >
      <div class="canvas-box-all" v-show="canvasShow == true">
        <img class="canvasImg" :src="canvasUrl" alt />
        <p>长按保存图片</p>
      </div>
      <div v-show="canvasShow == false" class="completed-box" id="all-sign">
        <h3>奖学金证明</h3>
        <div class="avatar">
          <img
            class="head"
            :src="
              myData.head_img ||
              require('../../../assets/image/active/signAct/chick.png')
            "
            alt
          />
          <img
            class="scarf"
            src="../../../assets/image/active/signAct/scarf.png"
            alt
          />
        </div>
        <span>恭喜{{ myData.real_name }}同学</span>
        <p>您已经完成全部挑战，成功激活奖学金</p>
        <div class="img-box">
          <div class="flag">已激活</div>
          <!-- todo -->
          <img class="coupon" src="https://static.yzou.cn/zmc/a20240704ChengJian/coupons.png" alt />
        </div>
        <div class="sign-box">
          <div class="top">
            <span>校长：</span>
            <img src="../../../assets/image/active/signAct/sign.png" alt />
          </div>
          <p>日期：{{ successTime | formatDate("yyyy.MM.dd") }}</p>
        </div>
      </div>
      <button
        v-show="canvasShow == false"
        class="save-btn"
        style="
          position: absolute;
          bottom: -50px;
          left: 50%;
          transform: translateX(-50%);
        "
        @click="saveImg()"
      >
        保存图片
      </button>
    </van-popup>
    <!-- 跑步完成后弹框 -->
    <van-popup
      round
      v-model="runSign"
      @click-overlay="saveClickOpt('runningStatus')"
    >
      <div class="canvas-box" v-show="canvasShow == true">
        <div class="main-box">
          <img class="canvasImg" :src="canvasUrl" alt />
        </div>
        <p>长按保存图片</p>
      </div>
      <div class="completed-run" v-show="canvasShow == false">
        <div class="main-box" id="run-sign">
          <img
            src="../../../assets/image/active/signAct/running-sign.png"
            alt
          />
          <p>恭喜{{ myData.real_name }}同学</p>
          <p>您已经完成「21天跑步」挑战！</p>
        </div>
        <button @click="saveRunImg()">保存图片</button>
        <p
          style="
            text-align: center;
            width: 100%;
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          "
        >
          *您还有学习的挑战要完成哦，切勿提前松懈下来～
        </p>
      </div>
    </van-popup>
    <!-- 学习完成后弹框 -->
    <van-popup
      round
      v-model="studySign"
      @click-overlay="saveClickOpt('studyStatus')"
    >
      <div class="canvas-box" v-show="canvasShow == true">
        <div class="main-box">
          <img class="canvasImg" :src="canvasUrl" alt />
        </div>
        <p>长按保存图片</p>
      </div>
      <div class="completed-run" v-show="canvasShow == false">
        <div class="main-box" id="study-sign">
          <img src="../../../assets/image/active/signAct/study-sign.png" alt />
          <p>恭喜{{ myData.real_name }}同学</p>
          <p>您已经完成「21天学习」挑战！</p>
        </div>
        <button @click="saveStudyImg()">保存图片</button>
        <p
          style="
            text-align: center;
            width: 100%;
            position: absolute;
            bottom: -50px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 13px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
          "
        >
          *您还有跑步的挑战要完成哦，切勿提前松懈下来～
        </p>
      </div>
    </van-popup>
  </div>
</template>

<script>
import html2canvas from "html2canvas";
import LeaderBoard from "./components/LeaderBoard.vue";
import RunBonus from "./components/RunBonus.vue";
import Rule from "./components/Rule.vue";
import StudyBonus from "./components/StudyBonus.vue";
import {
  toLogin,
  finActScholarshipUrl,
  isActScholarship,
} from "../../../common";
import share from "@/components/share";
import { Popup, Toast, Dialog } from "vant";
import newcountdown from "@/components/active/countdown6";
import swipers from "@/components/activePage/swipers";
import { imgBaseURL } from "../../../config";
import SecondFooter from "../2021NewMain/components/second-footer";
import inviteTop from "../enrollAggregate/components/invite-top";
// todo
// 优惠类型还需要改这两个文件
// 1.src\common\index.js isNewScholarship方法, isActScholarship方法, finActScholarshipUrl方法
// 2.src\view\invite\enroll.vue 这个文件的scholarshipArr相关的变量
let scholarship = "202"; //优惠类型

import { imgPosterBaseURL } from "@/config/index";

export default {
  data() {
    return {
      isPay: false,
      activeName: "recruit",
      animate: false,
      isIphoneX: false,
      shareLink: "",
      isLogin: null,
      scholarship: scholarship,
      inviteId: "",
      showInvite: false,
      items: [],
      invite: {},
      storyList: [],
      imgUrlL: "http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/ic_launcher.png",
      shareObj: { title: "", desc: "", img: "" },
      // todo
      title: "5000元奖学金报读活动，限时参加！",
      // todo
      desc: "活动旨在鼓励更多的社会人士成为上进青年，帮助他们实现自己的大学梦！",
      content: "",
      EnrolmentCount: "",
      learnInfos: [],
      enrollEnd: false, //停止报名
      startTime: "",
      endTime: "",
      show: false,
      isScrollEnrollList: true,
      showCard: false,
      cardHeadUrl: "",
      cardMobile: "",
      cardNickname: "",
      cardWechatQrcode: "",
      company: "",
      motto: "",
      position: "",
      propagateUrl: "",
      flag: false,
      isShow: false,
      headLimit: "?x-oss-process=image/resize,m_fixed,h_30,w_30",
      actName: "",
      activityContent: {},
      limitCount: 0,
      limitCountShow: 0,
      learnCount: 0,
      isShowEnd: 0,
      // todo
      activityId: 202, // 奖学金活动ID
      runData: {}, // 跑步打卡
      studyData: {}, // 读书打卡
      couponStatus: false, // 是否领取优惠券
      semesterData: {}, // 学期信息
      recordList: [], // 新一轮挑战列表日期数据
      showNewAct: false, // 选择新一轮挑战
      confirmShow: false, // 确认选择挑战弹框
      // todo
      activationShow: false, // 领取优惠卷弹框
      // todo
      signShow: false, // 双打卡完成弹框
      runSign: false, //跑步完成弹框
      studySign: false, //学习完成弹框
      codeShow: false, // 二维码弹框
      ruleShow: false, // 规则说明弹框
      newTime: null, // 新的挑战日期
      semesterId: null, // 学期id
      canvasUrl: "",
      qrCodeUrl: null,
      patchCard: [], // 补卡信息
      newCard: [], // 正常打卡信息
      canvasShow: false,
      rankingList: [], // 排行榜数据
      pageNum: 1,
      pageSize: 6,
      myData: {}, // 个人排行榜数据
      mobile: "", // 跟进人电话
      rankingLength: null,
      showNoMore: false,
      nowSemesterId: null,
      successTime: "", // 打卡成功时间
    };
  },
  created() {
    this.isLogin = !!this.storage.getItem("authToken");
    (this.inviteId = this.$route.query.inviteId || ""),
      (this.action = this.$route.query.action || "");
    // todo
    this.shareLink = `${window.location.origin}/active/a20240704ChengJian`;
    this.activeName = this.$route.query.tab || "recruit";
    this.getNewRegList();
    this.getActivityInfo();
    if (this.action === "share" && !this.isLogin) {
      toLogin.call(this, null);
    }
    setInterval(this.scroll, 3000);
    setInterval(this.scrollText, 4000);
    if (this.isLogin) {
      this.initData();
      this.getCurrentAct();
    }
  },
  components: {
    LeaderBoard,
    RunBonus,
    StudyBonus,
    share,
    Popup,
    Rule,
    swipers,
    newcountdown,
    SecondFooter,
    inviteTop,
  },
  computed: {
    jumpUrl: function () {
      let url = {
        name: "adultExamEnrollCheck",
        query: {
          activityName: "scholarship",
          inviteId: this.inviteId,
          regOrigin: "62",
          action: "login",
          scholarship: this.scholarship,
          actName: this.actName,
        },
      };
      return url;
    },
  },
  mounted: function () {
    this.successTime = Date.now();
    //获取邀约人信息
    if (!!this.inviteId) {
      this.getInviteInfo();
    }
  },
  methods: {
    getCurrentAct() {
      this.$http.post("/mkt/stdLearnInfo/1.0/").then((res) => {
        let { code, body } = res;
        if (code !== "00") return;
        //遍历优惠类型是否参加奖学金
        body.learnInfos.forEach((item) => {
          if (
            isActScholarship(item.scholarship) &&
            item.scholarship !== this.scholarship
          ) {
            let currentActRoute = finActScholarshipUrl(item.scholarship);
            Dialog.confirm({
              title: "温馨提示",
              confirmButtonText: "前往我的活动",
              cancelButtonText: "继续浏览",
              message: "你已报名了其他活动是否跳转原活动页面!",
            })
              .then(() => {
                this.$router.push({
                  name: currentActRoute.routeName,
                  params: currentActRoute.params
                });
              })
              .catch(() => {
                // on cancel
              });
          }
        });
      });
    },
    saveClickOpt(name) {
      localStorage.setItem(name, true);
    },
    saveStudyImg() {
      localStorage.setItem("studyStatus", true);
      this.$nextTick(() => {
        html2canvas(document.getElementById("study-sign"), {
          width: document.getElementById("study-sign").offsetWidth,
          height: document.getElementById("study-sign").offsetHeight,
          useCORS: true,
        }).then((canvas) => {
          console.log(canvas.toDataURL());
          this.canvasUrl = canvas.toDataURL();
          this.canvasShow = true;
        });
      });
    },
    saveRunImg() {
      localStorage.setItem("runningStatus", true);
      this.$nextTick(() => {
        html2canvas(document.getElementById("run-sign"), {
          width: document.getElementById("run-sign").offsetWidth,
          height: document.getElementById("run-sign").offsetHeight,
          useCORS: true,
        }).then((canvas) => {
          console.log(canvas.toDataURL());
          this.canvasUrl = canvas.toDataURL();
          this.canvasShow = true;
        });
      });
    },
    saveImg() {
      localStorage.setItem("allStatus", true);
      this.$nextTick(() => {
        html2canvas(document.querySelector(".completed-box"), {
          width: document.getElementById("all-sign").offsetWidth,
          height: document.getElementById("all-sign").offsetHeight,
          useCORS: true,
        }).then((canvas) => {
          console.log(canvas.toDataURL());
          this.canvasUrl = canvas.toDataURL();
          this.canvasShow = true;
        });
      });
    },
    // 获取跟进人信息
    getFollowInfo() {
      this.$http.post("/mkt/getFollowInfo/1.0/").then((res) => {
        if (res.code == "00") {
          this.mobile = res.body.mobile || "4008336013";
        }
      });
    },
    getRecord(semesterId) {
      let data = {
        pageNum: this.pageNum,
        pageSize: this.pageSize,
        activityId: this.activityId,
        semesterId: semesterId,
      };
      this.nowSemesterId = semesterId;
      this.$http.post("/mkt/challengeRankList/1.0/", data).then((res) => {
        if (res.code == "00") {
          this.rankingLength = res.body.length - 1;
          this.rankingList = res.body.filter((el, index) => {
            if (index != 0) {
              return el;
            }
          });
          this.myData = res.body[0];
        } else {
          console.log("获取排行榜数据出错！");
        }
      });
    },
    loadMore() {
      let data = {
        pageNum: this.pageNum,
        pageSize: (this.pageSize += 10),
        activityId: this.activityId,
        semesterId: this.nowSemesterId,
      };
      console.log(data);
      this.$http.post("/mkt/challengeRankList/1.0/", data).then((res) => {
        if (res.code == "00") {
          if (this.rankingLength != res.body.length - 1) {
            this.rankingLength = res.body.length - 1;
            this.rankingList = res.body.filter((el, index) => {
              if (index != 0) {
                return el;
              }
            });
          } else {
            this.showNoMore = true;
            Toast("暂无更多数据！");
          }
        }
      });
    },
    // 初始化数据
    initData() {
      this.getChallengeData();
      // this.getCodeData();
      this.getFollowInfo();
    },
    // 领取优惠券
    getCoupon() {
      localStorage.setItem("coponStatus", true);
      this.activationShow = false;
      // Toast.loading({
      //   message: "领取中...",
      //   forbidClick: true,
      //   duration: 0,
      // });
      // let data = {
      //   activityId: this.activityId,
      // };
      // let obj = {
      //   couponType: "advance.witness.act.202301.cj",
      // };
      // console.log(data);
      // this.$http.post("/mkt/giveCouponByType/1.0/", obj).then((res) => {
      //   console.log(res);
      //   if (res.code == "00") {
      //     this.$http.post("/mkt/recordCoupon/1.0/", data).then((result) => {
      //       console.log(result);
      //       if (result.code == "00") {
      //         Toast.clear;
      //         Toast.success("领取成功!");
      //         this.getChallengeData();
      //       } else {
      //         Toast.fail("领取失败!");
      //         this.getChallengeData();
      //       }
      //     });
      //   }
      // });
    },
    // 获取上进奖学金挑战信息
    getChallengeData() {
      let data = {
        activityId: this.activityId,
      };
      this.$http.post("/mkt/isExistChallenge/1.0/", data).then((res) => {
        if (res.code == "00") {
          let {
            runningRemainDays,
            challengeStartTime,
            challengeStatus,
            remainChallengeCount,
            nowChallengeSemesterId,
            lackCardCount,
            existChallenge,
            isGetCoupon,
            learnCount,
            learnEndTime,
            learnStatus,
            patchCardCount,
            planPunchCardVos,
            runningCount,
            runningEndTime,
            runningStatus,
            todayRunningStatus,
          } = res.body;
          this.isPay = res.body.existChallenge;
          if (this.isPay) {
            this.getRecord(nowChallengeSemesterId);
          }
          console.log("🚀 ~ file: res.body", res.body);
          if (planPunchCardVos != null) {
            // 打卡状态
            this.newCard = planPunchCardVos.filter((el) => {
              if (
                el.start == true &&
                el.punchTime == null &&
                el.ageTime == false
              ) {
                return el;
              }
            });
            // 补卡状态
            this.patchCard = planPunchCardVos.filter((el) => {
              if (
                el.start == true &&
                el.punchTime == null &&
                el.ageTime == true
              ) {
                return el;
              }
            });
          }
          // 全部完成弹出框
          if (
            localStorage.getItem("allStatus") == null &&
            challengeStatus == 3
          ) {
            this.signShow = challengeStatus == 3 ? true : false;
          }

          // 判断是否跑步全部完成弹框;
          if (
            localStorage.getItem("runningStatus") == null &&
            challengeStatus != 3
          ) {
            this.runSign = runningStatus == 2 ? true : false;
          }
          // 判断是否学习全部完成弹框
          if (
            localStorage.getItem("studyStatus") == null &&
            challengeStatus != 3
          ) {
            this.studySign = learnStatus == 2 ? true : false;
          }
          // 判断是否领取了优惠券;
          if (existChallenge && localStorage.getItem("coponStatus") == null) {
            // this.activationShow = isGetCoupon == 0 ? true : false;
            this.activationShow = true;
          }
          // 判断是否成功激活优惠券
          this.couponStatus = challengeStatus == 3 ? true : false;

          this.runData = {
            runningRemainDays,
            challengeStatus,
            runningCount,
            todayRunningStatus,
            runningStatus,
            challengeStartTime,
            runningEndTime,
            remainChallengeCount,
            nowChallengeSemesterId,
          };
          this.studyData = {
            lackCardCount,
            isGetCoupon,
            nowChallengeSemesterId,
            challengeStatus,
            learnCount,
            learnStatus,
            challengeStartTime,
            learnEndTime,
            patchCardCount,
            planPunchCardVos,
            remainChallengeCount,
          };
          this.recordList = [];
          // 获取学期列表
          this.$http.post("/mkt/getSemesterList/1.0/", data).then((res) => {
            if (res.code == "00") {
              let time = Date.now();
              for (let i = 0; i < res.body.length; i++) {
                if (
                  res.body[i].semesterId !=
                    this.runData.nowChallengeSemesterId ||
                  res.body[i].attendTime < time
                ) {
                  console.log(res.body[i]);
                  this.recordList.push(res.body[i]);
                }
              }
              // 判断是否挑战失败重新选择学期
              if (remainChallengeCount != 0) {
                this.showNewAct = challengeStatus == 4 ? true : false;
              }
            }
          });
          // 获取学期基本信息----二维码信息
          this.$http
            .post("/pu/getSemesterInfo/2.0/", {
              semesterId: this.runData.nowChallengeSemesterId,
            })
            .then((res) => {
              if (res.code == "00") {
                this.semesterData = res.body;
                console.log(
                  "🚀 ~ file: index.vue ~ line 791 ~ this.$http.post ~ this.semesterData",
                  this.semesterData
                );
                this.qrCodeUrl =
                  imgPosterBaseURL + this.semesterData.wechatQrCode;
              }
            });
        }
      });
    },
    // 获取学期基本信息----二维码信息
    getCodeData() {
      let data = {
        semesterId: "1",
      };
      this.$http.post("/pu/getSemesterInfo/2.0/", data).then((res) => {
        if (res.code == "00") {
          this.semesterData = res.body;
          console.log(
            "🚀 ~ file: index.vue ~ line 791 ~ this.$http.post ~ this.semesterData",
            this.semesterData
          );
          this.qrCodeUrl = imgPosterBaseURL + this.semesterData.wechatQrCode;
        }
      });
    },
    // 获取学期列表
    getStudyList() {
      let data = {
        activityId: this.activityId,
      };
      this.$http.post("/mkt/getSemesterList/1.0/", data).then((res) => {
        if (res.code == "00") {
          let time = Date.now();
          for (let i = 0; i < res.body.length; i++) {
            console.log(
              "this.runData.nowChallengeSemesterId",
              this.runData.nowChallengeSemesterId
            );
            console.log(
              res.body[i].semesterId == this.runData.nowChallengeSemesterId
            );
            console.log(res.body[i].attendTime < time);
            if (
              res.body[i].semesterId == this.runData.nowChallengeSemesterId ||
              res.body[i].attendTime < time
            ) {
              console.log(res.body[i]);
              this.recordList = res.body[i];
            }
          }
        }
      });
    },
    // 展示确认挑战框
    showConfirmBox(semesterId, time) {
      this.confirmShow = true;
      this.newTime = time;
      this.semesterId = semesterId;
    },
    // 选择新的挑战
    addNewStudy() {
      this.confirmShow = false;
      Toast.loading({
        message: "选择中...",
        forbidClick: true,
        duration: 0,
      });
      let data = {
        activityId: this.activityId,
        semesterId: this.semesterId,
      };
      console.log(data);
      this.$http.post("/mkt/addChallenge/1.0/", data).then((res) => {
        if (res.code == "00") {
          Toast.clear;
          Toast.success("选择成功!");
          console.log("🚀 ~ file: ~ res", res);
          this.showNewAct = false;
          this.initData();
        } else {
          Toast.fail("选择失败!");
          this.initData();
        }
      });
    },
    enroll() {
      this.$router.push(this.jumpUrl);
    },
    gonew() {
      this.$router.push({
        name: "newDreamBuild",
        query: { inviteId: this.inviteId },
      });
    },

    lookMoreStotyList() {
      this.$router.push({
        name: "scholarshipStory",
        query: { inviteId: this.inviteId },
      });
    },

    bannerJump: function (url) {
      if (url) {
        const origin = window.location.origin;
        if (url.startsWith(origin)) {
          if (url === "https://zm.yzou.cn/active/redEnveloped") {
            window.location.replace(url);
          } else {
            this.$router.push(url.replace(origin, ""));
          }
        } else {
          window.location.href = url;
        }
      }
    },
    nowGB() {
      const now = new Date().getTime();
      // if (this.EnrolmentCount <= 0 || now > this.endTime) {
      if (now > this.endTime) {
        this.$modal({
          message: "当前活动已结束，可以联系助学老师或者400-833-6013",
          icon: "warning",
        });
        return;
      }
      if (now < this.startTime) {
        this.$modal({
          message: "活动暂未开始",
          icon: "warning",
        });
        return;
      }
      this.$router.push({
        name: "adultExamEnrollCheck",
        query: {
          action: "login",
          activityName: "scholarship",
          inviteId: this.inviteId,
          scholarship: this.scholarship,
          actName: this.actName,
        },
      });
    },
    phoneNumberMobile() {
      window.location.href = "tel:" + this.cardMobile;
    },
    //活动信息
    getActivityInfo() {
      this.$http
        .post("/mkt/getActivityInfo/1.0/", { scholarship: scholarship })
        .then((res) => {
          let { code, body } = res;
          if (code !== "00") return;
          this.EnrolmentCount = body.limitCount - body.learnCount;
          this.startTime = body.StartTime;
          this.endTime = body.EndTime;
          this.actName = body.actName;
          this.limitCount = body.limitCount;
          this.learnCount = body.learnCount;
          this.limitCountShow = body.limitCountShow;
          this.isShowEnd = body.isShowEnd;
          const now = new Date().getTime();
          if (this.EnrolmentCount <= 0 || now > this.endTime) {
            this.EnrolmentCount = 0;

            this.enrollEnd = true;
          }
          if (now < this.startTime) {
            this.enrollEnd = true;
          }
          if (body.learnCount == "0") {
            this.isScrollEnrollList = false;
          }
        });
    },
    enrollEndTip() {
      const now = new Date().getTime();
      if (now < this.startTime) {
        this.$modal({ message: "活动暂未开始", icon: "warning" });
        return;
      }
      if (now > this.endTime) {
        this.$modal({
          message: "来迟一步啦，当前名额已满，请联系助学老师。",
          icon: "warning",
        });
        return;
      }
    },
    // 轮播文字
    scrollText() {
      this.animate = true;
      setTimeout(() => {
        this.items.push(this.items[0]);
        this.items.shift();
        this.animate = false;
      }, 700);
    },
    getArtile(id) {
      this.$http
        .post("/mkt/getSpreadCardByRelationId/1.0/ ", { relationId: id })
        .then((res) => {
          if (res.code === "00") {
            if (res.body === null) {
              this.articleId = "";
            } else {
              this.showCard = true;
              Object.assign(this.$data, res.body);
              if (this.cardHeadUrl) {
                this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
              }
              if (this.cardWechatQrcode) {
                this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
              }
              this.propagateUrl = this.propagateUrl + "&articleId=" + id;
            }
          }
        });
    },

    getCard(userId) {
      this.$http
        .post("/mkt/getFollowCardByUserId/1.0/", { userId: userId })
        .then((res) => {
          let { code, body } = res;
          if (code !== "00") return;
          if (res.body === null) {
            this.articleId = "";
          } else {
            this.showCard = true;
            Object.assign(this.$data, res.body);
            if (this.cardHeadUrl) {
              this.cardHeadUrl = imgBaseURL + this.cardHeadUrl;
            }
            if (this.cardWechatQrcode) {
              this.cardWechatQrcode = imgBaseURL + this.cardWechatQrcode;
            }
            this.propagateUrl = this.propagateUrl + "&articleId=" + id;
          }
        });
    },
    // 获取邀约人信息
    getInviteInfo() {
      let inviteId = (
        window.sessionStorage.getItem("inviteId") ||
        decodeURIComponent(
          this.$route.query.inviteId ||
            this.getQueryString(this.redirect, "inviteId") ||
            ""
        )
      ).replace(/ /g, "+");
      if (inviteId) {
        this.$http
          .post("/us/getInviteInfo/1.0/", { inviteToken: inviteId })
          .then((res) => {
            let { code, body } = res;
            if (code !== "00") return;
            this.invite = body || {};
            this.showInvite = true;
            if (this.invite.relation == "2" || this.invite.relation == "6") {
              this.getArtile(this.invite.userId);
            } else {
              this.getCard(this.invite.userId);
            }
          });
      } else {
      }
    },
    // 邀请好友报名
    openShare: function () {
      this.$router.push({ name: "dreamBuildInvite" });
    },
    tips() {
      this.$modal({ message: "活动暂未开始！", icon: "warning" });
    },

    phoneNumber() {
      if (this.articleId) {
        window.location.href = "tel:" + this.cardMobile;
        return;
      }
      window.location.href = "tel:4008336013";
    },

    // 获取最新注册用户列表
    getNewRegList: function () {
      this.$http.post("/us/getNewRegList/1.0/").then((res) => {
        let { code, body } = res;
        if (code === "00") {
          this.items = body;
        }
      });
    },
  },
};
</script>

<style lang="less" scoped>
@rem: 0.01rem;
@import "../../../assets/less/variable.less";

.top-pic {
  width: 100%;
  height: 1.63rem;
  background: white;
}

.topPic {
  width: 3.55rem;
  height: 1.62rem;
  background-size: 100% 100%;
  margin: 0 auto;
}

.Storylist {
  .list-banner {
    height: 1.48rem;
    z-index: 1;
    background: white;
  }

  .invite {
    background-image: url("../../../assets/image/active/enrollmentHomepage/invitBg2.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 0.68rem;
    position: relative;
    margin-bottom: -0.1rem;
    z-index: 99;

    img {
      width: 0.48rem;
      height: 0.48rem;
      float: left;
      border-radius: 50%;
      margin-top: 1.8%;
      margin-left: 0.12rem;
      margin-right: 0.07rem;
    }

    .rightView {
      float: left;
      margin-top: 2.3%;

      p {
        font-size: 0.16rem;

        span {
          color: #e15443;
          font-size: 0.13rem;
        }

        &:first-of-type {
          font-size: 0.13rem;
        }
      }
    }
  }

  .item {
    display: block;
    background-color: #fff;
    position: relative;
    padding: 0.2rem 0.16rem 0.1rem 0.16rem;

    &:before {
      .borderBottom;
    }

    .fl {
      width: 69%;

      p {
        line-height: 1.5;
      }
    }

    .fr {
      width: 30%;
      text-align: right;
    }

    .title {
      font-size: 0.15rem;
      color: #363636;
      min-height: 0.54rem;
      overflow: hidden;
    }

    .date {
      margin-top: 0.1rem;

      .heart {
        width: 1.5rem;
        float: left;

        span {
          display: block;
          float: left;
          margin-right: 0.17rem;
          padding-left: 0.21rem;
          line-height: 0.18rem;
          overflow: hidden;

          &.heartbeat {
            background: url("../../../assets/image/scholarshipStory/ic_school_like.png")
              no-repeat;
            background-size: 0.18rem;

            &.active {
              background: url("../../../assets/image/scholarshipStory/ic_school_like_select.png")
                no-repeat;
              background-size: 0.18rem;
            }
          }

          &.read {
            background: url("../../../assets/image/scholarshipStory/ic_school_like copy.png")
              no-repeat 0 0.01rem;
            background-size: 0.16rem;
          }
        }
      }

      font-size: 0.12rem;
      color: rgba(54, 54, 54, 0.6);
    }

    .description {
      font-size: 0.12rem;
      color: #444;
    }

    .pic {
      width: 0.72rem;
      height: 0.72rem;
      object-fit: cover;
    }
  }
}

.popupBoxTwo {
  width: 3.1rem;
  height: 1.8rem;

  img {
    width: 3.1rem;
    height: 1.8rem;
  }
}

.popupBoxThree {
  text-align: center;
  background: #fff;
  width: 2.88rem;
  height: 3.55rem;
  border-radius: 4px;

  .titleImg {
    width: 1.1rem;
    height: 0.33rem;
    margin-top: 0.27rem;
  }

  .firstText {
    font-size: 0.14rem;
    color: rgba(54, 54, 54, 1);
    width: 2.5rem;
    margin: 0 auto;
    margin-top: 0.22rem;
    text-align: left;

    span {
      color: #ed5206;
    }
  }

  div {
    overflow: hidden;

    .indexImgOne,
    .indexImgTwo {
      width: 0.15rem;
      height: 0.15rem;
      float: left;
      margin-left: 0.2rem;
      margin-top: 0.03rem;
    }

    .secondText,
    .threeText {
      width: 2.25rem;
      float: left;
      text-align: left;
      margin-left: 0.04rem;
    }
  }

  .firstDiv {
    margin-top: 0.2rem;
  }

  button {
    width: 2.38rem;
    height: 0.4rem;
    border: none;
    border-radius: 0.2rem;
    font-size: 0.15rem;
    color: rgba(237, 82, 6, 1);
    background: rgba(255, 255, 255, 1);
  }

  .confirm {
    background: rgba(237, 82, 6, 1);
    color: rgba(255, 255, 255, 1);
    margin-top: 0.33rem;
  }

  .giveUp {
    margin-top: 0.05rem;
  }
}

.imageWrapper {
  width: 3rem;

  img {
    width: 100%;
  }
}

.downText {
  color: #ffffff;
  font-size: 0.15rem;
  margin-top: 0.15rem;
}

.saveText {
  color: #ffffff;
  font-size: 0.15rem;
  margin-bottom: 0.1rem;
}

.newMain {
  position: relative;
  background: #ffffff;

  &.active {
    padding-bottom: 34px;
  }

  .invite {
    background-image: url("../../../assets/image/active/enrollmentHomepage/invitBg2.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    height: 0.68rem;
    position: relative;
    margin-bottom: -0.1rem;
    z-index: 99;

    img {
      width: 0.48rem;
      height: 0.48rem;
      float: left;
      border-radius: 50%;
      margin-top: 1.8%;
      margin-left: 0.12rem;
      margin-right: 0.07rem;
    }

    .rightView {
      float: left;
      margin-top: 2.3%;

      p {
        font-size: 0.16rem;

        span {
          color: #e15443;
          font-size: 0.13rem;
        }

        &:first-of-type {
          font-size: 0.13rem;
        }
      }
    }
  }

  .banner {
    position: relative;
    height: 0.5rem;
    background-color: rgba(255, 255, 255, 1);

    .schoolTrademarkImg {
      width: 0.57rem;
      position: absolute;
      right: 0.18rem;
      top: 0.13rem;
    }

    .content {
      position: absolute;
      bottom: 0;
      width: 100%;

      &.bg-g {
        background-image: none;
      }

      .tab-3 {
        height: 0.5rem;
        margin-top: -0.02rem;
        background-color: #ff970303;

        p {
          float: left;
          text-align: center;
          // padding: 0.1rem 0;
          width: 1.25rem;
          height: 0.5rem;
          line-height: 0.5rem;

          span {
            display: inline-block;
            width: 100%;
            // border-right: 0.01rem rgba(23, 6, 6, 0.09);
            font-size: 0.14rem;
            /*color: rgba(13, 139, 58, 1);*/
            color: rgba(69, 56, 56, 1);
            font-family: SourceHanSansCN-Normal, sou;
            font-weight: 400;
          }

          &.active {
            span {
              position: relative;
              width: auto;
              border-right: none;
              /*color: rgba(13, 139, 58, 1);*/
              font-family: SourceHanSansCN-Bold, SourceHanSansCN;
              font-weight: bold;
              border-radius: 0.02rem;
              color: rgba(193, 58, 54, 1);

              &:before {
                content: "";
                height: 0.04rem;
                /*background-color: rgba(13, 139, 58, 1); */
                background: rgba(193, 58, 54, 1);
                width: 0.52rem;
                top: 0.46rem;
                transform: translateX(-50%);
                left: 50%;
                position: absolute;
                border-radius: 2px;
              }
            }
          }

          &:last-of-type {
            span {
              border-right: none;
            }
          }
        }
      }
    }
  }

  .headBanner {
    width: 3.75rem;
    height: 4rem;
    // background: url("./img/bg-banner.png") no-repeat center;
    // todo
    background: url("https://static.yzou.cn/zmc/a20240704ChengJian/bg.png") no-repeat center;
    background-size: 100% 100%;
    position: relative;

    .textContentJoin {
      position: absolute;
      right: 0.1rem;
      // top: 3.4rem;
      bottom: 0.06rem;
      width: 2.4rem;
      height: 0.2rem;
      overflow: hidden;
      margin-top: 0.04rem;
      background: rgba(26, 26, 26, 0.5);
      border-radius: 0.2rem;

      .gdBox {
        width: 100%;
        background-color: rgba(0, 0, 0, 0);
        background-color: rgba(255, 170, 34, 0.2);

        p {
          font-size: 0.11rem;
          height: 0.22rem;
          line-height: 0.22rem;
          display: flex;
          justify-content: center;
          -webkit-justify-content: center;
          position: relative;

          img {
            width: 0.21rem;
            height: 0.21rem;
            border-radius: 50%;
            margin-top: 0;
            position: absolute;
            left: 0;
          }

          span {
            margin-left: 0.11rem;
            display: inline-block;
            font-size: 0.11rem;
            float: left;
            color: rgba(255, 255, 255, 1);

            &:first-of-type {
              max-width: 0.7rem;
              overflow: hidden;
              height: 0.2rem;
              text-overflow: ellipsis;
            }
          }

          .name {
            width: 0.25rem;
            margin-left: 0.25rem;
          }
        }

        &.anim {
          transition: all 1s;
          margin-top: -0.25rem;
        }
      }
    }

    .schoolName {
      position: absolute;
      top: 0.49rem;
      left: 0.79rem;
      width: 2.17rem;
      height: 0.45rem;
    }

    .new-dream {
      position: absolute;
      top: 2.25rem;

      .dream {
        width: 3.03rem;
        height: 1.52rem;
        position: absolute;
        left: 0.4rem;
      }

      .left-line {
        width: 0.17rem;
        height: 0.7rem;
        position: absolute;
        top: 0.11rem;
        left: 0.13rem;
        margin-right: 0.13rem;
      }

      .right-line {
        width: 0.22rem;
        height: 0.92rem;
        margin: 0 0.05rem 0 0.05rem;
        position: absolute;
        top: 0.21rem;
        left: 3.48rem;
      }
    }
  }

  .redgo {
    display: flex;
    padding-left: 0.5rem;
  }

  .gotop {
    width: 100%;
    height: 1.59rem;
    position: absolute;
    top: 2.25rem;
    z-index: 99;
    justify-content: center;

    .frist {
      height: 1.5rem;
    }

    .bgm {
      width: 100%;
    }
  }

  .reGo {
    width: 0.57rem;
    height: 0.17rem;
    position: relative;
    top: 0.05rem;
  }

  .dian {
    width: 100%;
    height: 0.8rem;
  }

  .bonus {
    width: 3.75rem;
    height: 2.87rem;
    background: linear-gradient(180deg, #ff9745 0%, #ff4340 100%);
    position: relative;

    .bonus-child {
      width: 3.63rem;
      height: 2.8rem;
      background: url("../../../assets/image/active/signAct/bg2.png") no-repeat
        center;
      background-size: 100% 100%;
      margin: 0 auto;
      position: absolute;
      left: 0;
      right: 0;
      top: 0.05rem;

      .applic-btn {
        display: block;
        width: 1.86rem;
        height: 0.44rem;
        margin: 0 auto;
        margin-top: 0.14rem;
        image-rendering: -moz-crisp-edges;
        image-rendering: -o-crisp-edges;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        -ms-interpolation-mode: nearest-neighbor;
      }
      .tips-img {
        width: 2.32rem;
        height: auto;
        display: block;
        margin: 0 auto;
        margin-top: 0.18rem;
        image-rendering: -moz-crisp-edges;
        image-rendering: -o-crisp-edges;
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        -ms-interpolation-mode: nearest-neighbor;
      }

      .ticket {
        position: absolute;
        top: 0.12rem;
        left: 0;
        right: 0;
        margin: auto;
        width: 3.29rem;
        height: 1.01rem;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .line {
        position: absolute;
        top: 1.2rem;
        width: 3.26rem;
        height: 1px;
        border: 1px rgba(255, 111, 65, 0.5) dashed;
        left: 0;
        right: 0;
        margin: auto;
      }

      .ticket_detail {
        position: absolute;
        top: 1.3rem;
        left: 0;
        right: 0;
        margin: auto;
        width: 2.61rem;
        height: 1.64rem;

        .ul {
          width: 100%;
          display: flex;
          justify-content: center;
          .li {
            display: flex;
            align-items: center;
            padding-left: 0.06rem;

            img {
              height: 0.14rem;
              width: 0.13rem;
              margin-right: 0.06rem;
            }

            span {
              height: 0.2rem;
              font-size: 0.14rem;
              font-family: PingFang-SC-Bold, PingFang-SC;
              font-weight: bold;
              color: #f14d25;
              line-height: 0.2rem;
            }
          }
        }
        
        :last-child {
          margin-bottom: 0rem;
        }
      }

      .baoButton {
        width: 1.86rem;
        height: 0.44rem;

        background: linear-gradient(269deg, #f0513e 0%, #e61c26 100%);
        box-shadow: inset 0px 0px 3px 0px rgba(255, 255, 255, 0.5);
        border-radius: 0.25rem;
        border: 1px solid #ffbb56;
        line-height: 0.44rem;
        color: #fce7ba;
        font-size: 0.18rem;
        font-weight: 600;
        text-align: center;
        margin: 0 auto;
        margin-top: 0.14rem;
      }

      @keyframes free_download {
        0% {
          transform: scale(0.9);
        }

        100% {
          transform: scale(1);
        }
      }
    }
  }

  .schoolRecruit {
    width: 1.05rem;
    height: 0.2rem;
    background: url("../../../assets/image/active/oneYear/recruit.png")
      no-repeat;
    background-size: 100% 100%;
    margin: 0.24rem 0.31rem 0.15rem 1.31rem;
  }

  .proGress {
    text-align: center;
    padding-bottom: 0.1rem;

    img {
      width: 3.51rem;
      height: 0.58rem;
    }
  }

  .schoolnum {
    width: 100%;
    // padding-left: 0.08rem;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    padding-bottom: 0.15rem;

    img {
      width: 0.8rem;
      height: 0.8rem;
      margin-bottom: 0.07rem;
    }

    .school-detail {
      width: 50%;
      text-align: center;
      font-size: 0.14rem;
      font-family: PingFang-SC-Bold, PingFang-SC;
      font-weight: bold;
      color: #453838;
    }
  }

  .left-pic {
    position: fixed;
    right: 0;
    top: 40%;
    width: 1rem;
    height: 1rem;
    z-index: 9999;
    -webkit-animation: download 1s linear alternate infinite;
    animation: download 1s linear alternate infinite;

    @-webkit-keyframes download {
      0% {
        -webkit-transform: scale(0.9);
      }

      100% {
        -webkit-transform: scale(1);
      }
    }

    @keyframes download {
      0% {
        transform: scale(0.9);
      }

      100% {
        transform: scale(1);
      }
    }
  }

  .zhuanye {
    width: 0.96rem;
    height: 0.17rem;
    background: url("../../../assets/image/active/oneYear/major.png") no-repeat
      center;
    background-size: 100% 100%;
    margin-left: 1.4rem;
    margin-top: 0.15rem;
  }

  .userMessage {
    background: rgb(255, 255, 255);

    .headBox {
      height: 0.3rem;
      line-height: 0.3rem;
      position: relative;

      .headTitle {
        font-size: 0.15rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 0.9);
        text-align: center;
        margin-top: 1.5%;
        left: 42.8%;
      }

      .circleLeft {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 34%;
        top: 4%;
        pointer-events: none;
      }

      .circleRight {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 55.5%;
        top: 4%;
        pointer-events: none;
      }
    }
  }

  .info {
    width: 3.55rem;
    margin: 0 auto;

    .headBox {
      height: 0.3rem;
      line-height: 0.3rem;
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;

      .headTitle {
        font-size: 0.15rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 0.9);
        display: inline-block;
      }

      .headTitleIcon {
        width: 0.24rem;
        height: 0.24rem;
      }
    }

    .table {
      position: relative;
      border-radius: 0.1rem;
      background-color: #fff;
      // border-left: 1px solid rgba(239, 186, 6, 1);
      // border-right: 1px solid rgba(239, 186, 6, 1);
      // border-top: 1px solid rgba(239, 186, 6, 1);
      // border-bottom: 1px solid rgba(239, 186, 6, 1);
      border: 1px solid rgba(239, 186, 6, 1);
      overflow: hidden;
      margin-bottom: 0.18rem;

      .table-t {
        height: 0.82rem;
        padding-left: 0.12rem;
        padding-top: 0.1rem;
        background: rgba(255, 244, 219, 1);
        border-radius: 10px 10px 0px 0px;

        .schoolName {
          height: 0.22rem;
          font-size: 0.16rem;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: rgba(193, 57, 53, 1);
          line-height: 0.22rem;
        }

        .schoolPf {
          color: rgba(54, 54, 54, 0.8);
          font-size: 13px;
        }

        .fr {
          width: 1.87rem;
          height: 0.17rem;
          font-size: 0.12rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(193, 57, 53, 1);
          line-height: 0.17rem;
        }

        .fl,
        .fr {
          float: none;
        }
      }

      .table-b {
        table {
          width: 100%;
          // border-top: 1px solid rgba(239, 186, 6, 1);
          border-top: none;

          th {
            height: 0.2rem;
            font-size: 0.14rem;
            font-family: PingFang-SC-Bold, PingFang-SC;
            font-weight: bold;
            color: rgba(69, 56, 56, 1);
            line-height: 0.2rem;
          }

          td {
            width: 0.67rem;
            font-size: 0.12rem;
            font-weight: 600;
            vertical-align: middle;
            border-bottom: 1px solid rgba(239, 186, 6, 1);
            border-right: 1px solid rgba(239, 186, 6, 1);
            background-color: #fff;
            padding: 5px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: rgba(69, 56, 56, 1);
            line-height: 0.17rem;

            &:last-of-type {
              border-right: none;
            }

            span {
              color: rgba(193, 57, 53, 1);
              font-weight: bold;
              font-size: 0.14rem;
            }
          }

          tr:nth-last-of-type(1) td {
            border-bottom: none;
          }

          .text_top {
            height: 1.08rem;
          }

          .text_center {
            // height: 1.19rem;
          }

          .text_center-luang {
            height: 0.6rem;
          }

          .text-l {
            text-align: left;
            width: 2.16rem;
            font-size: 0.13rem;
            font-weight: 400;
            font-family: PingFangSC-Regular, PingFang SC;
            color: rgba(69, 56, 56, 1);
            line-height: 0.17rem;
          }

          .ric {
            color: #c13935;
          }

          .orage {
            color: #ed5206;
          }

          .text_name {
            width: 33.333333%;
            font-weight: 400;
            p {
              // line-height: 0.2rem;
              padding: 2px;
            }
          }

          .text_study {
            // width: 0.74rem;
            font-weight: 400;
          }

          .text_book {
            // width: 0.66rem;
            font-weight: 400;
            padding: 4px;
          }

          .text_system {
            width: 16.666667%;
            font-weight: 400;
            padding-left: 0.07rem;
            padding-right: 0.07rem;
          }
          .title_text {
            font-weight: 700;
            color: #444;
            font-size: 0.12rem;
            padding: 8px 0;
          }
        }

        .title_calss {
          height: 0.52rem;

          th {
            padding: 0 0.01rem;
            line-height: 0.52rem;
            border-radius: 0.1rem 0.1rem 0rem 0rem;
            background: #fffbf6;
            font-size: 0.18rem;
            color: #444;
            font-weight: 400;
          }
        }
      }

      &:after {
        content: "";
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: #ffd100;
        right: -0.07rem;
        top: -0.1rem;
        z-index: -1;
      }
    }
  }

  .sprint {
    width: 100%;
    max-width: 640px;
    margin: 0 auto;
    position: relative;
    background: white;
    padding-top: 0.2rem;

    .headTab {
      display: flex;
      justify-content: space-around;

      // margin-top: .15rem;
      .lfBag,
      .rightRecord {
        position: relative;
        width: 1.43rem;
        height: 0.38rem;
        line-height: 0.4rem;
        background: #fde9c0;
        text-align: center;
      }

      .lfBag {
        background: url("../../../assets/image/active/oneYear/mySholar.png")
          no-repeat;
        background-size: 100% 100%;

        span {
          position: absolute;
          right: 0.24rem;
          color: #fc421b;
          font-weight: bold;
          line-height: 0.35rem;
        }
      }

      .rightRecord {
        background: url("../../../assets/image/active/oneYear/myJl.png")
          no-repeat;
        background-size: 100% 100%;

        span {
          position: absolute;
          right: 0.35rem;
          color: #ff8700;
          font-weight: bold;
          line-height: 0.35rem;
        }
      }
    }

    .sprint_wrap {
      width: 3.35rem;
      height: auto;
      overflow: hidden;
      margin: auto;
      margin-top: 0.15rem;
      margin-bottom: 0.15rem;
      box-shadow: 0px 3px 22px 0px rgba(8, 1, 3, 0.15);

      .sprint_invite {
        width: 3.35rem;
        height: 4.2rem;

        &.active {
          height: auto;
          overflow: hidden;
        }

        // margin: 0.2rem;
        background-color: white;
        position: relative;
        overflow: hidden;
        margin: 0 auto;

        .headTxt {
          width: 100%;
          color: rgba(54, 54, 54, 1);
          line-height: 0.3rem;
          font-size: 0.17rem;
          text-align: center;
          font-weight: bold;
          margin: 0.25rem auto 0.15rem;
        }

        .sprintTxt {
          font-size: 0.14rem;
          color: rgba(54, 54, 54, 1);
          line-height: 0.23rem;
          width: 2.91rem;
          margin: 0 auto;
          text-indent: 2em;
        }

        .remark {
          width: 3.06rem;
          margin: 0.15rem auto 0;
          color: rgba(193, 57, 53, 1);
          font-size: 0.12rem;
          padding: 0.1rem 0.12rem 0 0.05rem;
          border-top: dashed 1px rgba(252, 66, 27, 0.35);
        }

        .toMethod {
          color: rgba(54, 54, 54, 0.6);
          line-height: 0.4rem;
          text-align: center;
          margin: 0.06rem 0 0.1rem;
        }

        .receive {
          display: block;
          width: 2.53rem;
          height: 0.44rem;
          background: url("../../../assets/image/active/oneYear/invite_btn.png");
          background-size: 100% 100%;
          margin: 0 auto;
          margin-top: 0.24rem;
        }

        .info_edit {
          position: absolute;
          top: 3.6rem;

          .headBox {
            width: 0.92rem;
            height: 0.3rem;
            text-align: center;
            margin: 0.4rem auto 0rem;
            color: rgba(54, 54, 54, 1);
            font-size: 0.15rem;
            position: relative;

            &:before {
              content: "";
              position: absolute;
              left: -0.4rem;
              top: 0rem;
              width: 0.39rem;
              height: 0.24rem;
              background-image: url("../../../assets/image/active/sprintBeforeExam/icon_left.png");
              background-size: 0.39rem;
            }

            &:after {
              content: "";
              position: absolute;
              right: -0.4rem;
              top: 0rem;
              width: 0.39rem;
              height: 0.24rem;
              background-image: url("../../../assets/image/active/sprintBeforeExam/icon_right.png");
              background-size: 0.39rem;
            }
          }

          p {
            width: 3.22rem;
            height: 0.5rem;
            margin-left: 0.26rem;
            padding-left: 0.2rem;

            &.red {
              color: rgba(252, 66, 27, 1);
              font-size: 0.12rem;
              line-height: 0.2rem;
              margin-top: 0.05rem;
            }

            span {
              display: block;
              float: left;
              height: 0.5rem;
              width: 0.63rem;
              text-align: justify;
              font-size: 0.14rem;
              line-height: 0.5rem;
              text-align-last: justify;
            }

            input {
              width: 2.21rem;
              height: 0.45rem;
              border: none;
              border-bottom: solid 1px rgba(54, 54, 54, 0.6);
            }
          }
        }

        .sprint_info {
          width: 3.06rem;
          padding-top: 0.1rem;
          position: relative;
          margin: 0.27rem auto 0;
          border-top: dashed 1px rgba(252, 66, 27, 0.35);

          img {
            width: 1.35rem;
            height: 0.56rem;
            position: absolute;
            right: 0.19rem;
            top: 0.28rem;
          }

          .info {
            p {
              font-size: 0.14rem;
              line-height: 0.14rem;
              color: rgba(54, 54, 54, 1);
              margin-bottom: 0.09rem;

              &:last-of-type {
                margin-bottom: 0;
              }
            }
          }

          .comment {
            margin-right: 0.15rem;
            margin-bottom: 0.19rem;

            span {
              color: rgba(252, 66, 27, 1);
            }

            margin-top: 0.3rem;
            line-height: 0.22rem;
            font-size: 0.14rem;
            color: rgba(54, 54, 54, 1);
          }
        }
      }
    }

    .sprint_list {
      background-color: white;
      height: auto;
      overflow: hidden;
      margin-top: 0.5rem;

      .headBox {
        height: 0.3rem;
        line-height: 0.3rem;
        position: relative;
        text-align: center;

        .headTitle {
          font-size: 0.15rem;
          font-weight: bold;
          color: rgba(218, 41, 47, 1);
          display: inline-block;
        }

        .headTitleIcon {
          width: 0.24rem;
          height: 0.24rem;
          margin-top: 0.02rem;
        }
      }

      .list {
        width: 100%;
        height: auto;
        overflow: hidden;

        .empty {
          margin: 0.5rem auto;
          width: 1rem;
          height: 1rem;
          padding-top: 0.83rem;
          background-image: url("../../../assets/image/active/sprintBeforeExam/no_sprint.png");
          background-size: 100%;
          background-repeat: no-repeat;
          text-align: center;
          color: rgba(54, 54, 54, 1);
        }

        ul {
          li {
            margin: 0.1rem auto;
            width: 3.52rem;
            height: auto;
            overflow: hidden;
            padding: 0.1rem;
            background-color: white;
            border-bottom: 1px solid rgba(54, 54, 54, 0.08);

            .head_info {
              .head {
                width: 0.38rem;
                height: 0.38rem;
                overflow: hidden;
                float: left;
                border-radius: 50%;

                img {
                  width: 100%;
                }
              }

              .info {
                float: left;
                width: 2.8rem;
                margin-left: 0.05rem;
                margin-top: 0.03rem;

                p {
                  clear: both;
                  margin-bottom: 0.1rem;
                  font-size: 0.14rem;

                  .name {
                    float: left;
                  }

                  .time {
                    float: right;
                    color: rgba(54, 54, 54, 0.4);
                    font-size: 0.12rem;
                    margin-top: 0;
                  }
                }
              }
            }

            .detail {
              clear: both;
              width: 3.04rem;
              padding: 0.1rem;
              border: dashed 0.01rem rgba(218, 41, 47, 1);
              margin-left: 0.3rem;
              font-size: 0.14rem;
              color: rgba(218, 41, 47, 1);
              line-height: 0.25rem;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;

              span {
                color: rgba(69, 56, 56, 1);
              }
            }
          }
        }
      }
    }

    .sprint_process {
      background-color: white;
      padding: 0.24rem 0 0 0;

      p {
        width: 100%;
        color: #363636;
        font-size: 0.15rem;
        text-align: center;
        margin-bottom: 0.12rem;
        font-weight: bold;
        font-family: PingFang-SC-Bold, PingFang-SC;
        color: rgba(69, 56, 56, 1);
      }

      img {
        display: block;
        margin: 0 auto;
        width: 3.55rem;
        height: 1.12rem;
      }
    }
  }

  .clear {
    clear: both;
  }

  .introducetText {
    // padding: 0.17rem 0.15rem 0.01rem 0.12rem;
    text-indent: 2em;
    width: 3.45rem;
    font-size: 0.14rem;
    color: rgba(54, 54, 54, 1);
    background: rgba(255, 255, 255, 1);
    margin: 0 auto;
    margin-top: 0.15rem;
  }

  .enroolWrap {
    position: relative;

    p {
      position: absolute;
      top: 0.11rem;
      left: 0.35rem;
      font-size: 0.12rem;
      color: rgba(255, 255, 255, 1);

      span {
        margin-left: 0.04rem;
      }
    }

    .scholarshipBg {
      width: 3.55rem;
      height: 1.18rem;
      margin: 0 auto;
      margin-top: 0.1rem;
      margin-left: 0.1rem;
    }
  }

  .giftPackage {
    position: relative;
    width: 3.53rem;
    height: 3.01rem;
    margin: 0 auto;
    background: url("../../../assets/image/active/gdLingNan/bg_bag.png")
      no-repeat center;
    background-size: 100% 100%;
    margin-top: 0.1rem;

    .enrollCount {
      position: absolute;
      display: inline-block;
      width: 1rem;
      font-weight: bold;
      color: rgba(142, 22, 19, 1);
      font-size: 0.13rem;
      right: 0.24rem;
      top: 0.23rem;
    }

    .textContentJoin {
      position: absolute;
      left: 0.24rem;
      top: 0.18rem;
      width: 1.7rem;
      height: 0.22rem;
      overflow: hidden;
      background-color: rgba(233, 72, 86, 0.08);
      border-radius: 23px 22px 22px 23px;
      margin-top: 0.04rem;

      .gdBox {
        width: 100%;
        background-color: rgba(0, 0, 0, 0);
        background-color: rgba(255, 170, 34, 0.2);

        :first-child {
        }

        p {
          font-size: 0.11rem;
          height: 0.22rem;
          line-height: 0.22rem;
          display: flex;
          justify-content: center;
          -webkit-justify-content: center;
          position: relative;

          img {
            width: 0.21rem;
            height: 0.21rem;
            border-radius: 50%;
            margin-top: 0;
            position: absolute;
            left: 0;
          }

          span {
            margin-left: 0.11rem;
            display: inline-block;
            font-size: 0.11rem;
            float: left;
            color: black;

            &:first-of-type {
              max-width: 0.7rem;
              overflow: hidden;
              height: 0.2rem;
              text-overflow: ellipsis;
            }
          }

          .mobile {
            margin-left: 0rem;
            color: rgba(54, 54, 54, 1);
          }

          .name {
            width: 0.25rem;
            margin-left: 0.25rem;
          }
        }

        &.anim {
          transition: all 1s;
          margin-top: -0.25rem;
        }
      }
    }

    .giftPackageContent {
      /*padding: 10px;*/
      ul {
        width: 2.9rem;
        padding: 0.68rem 0 0.2rem 0rem;
        margin: auto;

        li {
          width: 100%;
          font-size: 0.14rem;
          text-align: left;
          font-weight: 500;
          line-height: 0.22rem;
          margin-top: 0.19rem;

          img {
            float: left;
            width: 0.24rem;
            height: 0.24rem;
            margin-top: 1%;
          }

          .imgTxt {
            width: 2.7rem;
            padding-top: 0.03rem;
            padding-left: 0.05rem;
          }
        }

        p {
          color: rgba(23, 6, 6, 0.8);
          margin-left: 0.23rem;
          font-size: 0.15rem;
          color: rgba(54, 54, 54, 1);
          margin-top: -0.08rem;
        }
      }
    }

    button {
      position: absolute;
      bottom: 0.15rem;
      left: 21%;
      width: 1.95rem;
      height: 0.6rem;
      border: none;
      font-weight: bold;
      color: rgba(255, 255, 255, 1);
      font-size: 0.15rem;
      background: url("../../../assets/image/active/gdLingNan/btn_enroll.png")
        no-repeat center;
      background-size: 100% 100%;
    }
  }

  .activityRule {
    padding-bottom: 0.2rem;
    margin-top: 0.44rem;
    background-color: #fff;
    border-bottom: 1px solid rgba(54, 54, 54, 0.08);

    .headBox {
      height: 0.3rem;
      line-height: 0.3rem;
      position: relative;

      .headTitle {
        font-size: 0.15rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 0.9);
        text-align: center;
        margin-top: 1.5%;
        left: 42.8%;
      }

      .circleLeft {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 32%;
        top: 4%;
        pointer-events: none;
      }

      .circleRight {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 57.5%;
        top: 4%;
        pointer-events: none;
      }
    }
  }

  .enrollProcedure {
    background-color: #fff;
    text-align: center;
    padding-top: 0.2rem;
    border-bottom: 1px solid rgba(54, 54, 54, 0.08);
    padding-bottom: 0.2rem;

    .headBox {
      height: 0.3rem;
      line-height: 0.3rem;
      position: relative;

      .headTitle {
        font-size: 0.15rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 0.9);
        text-align: center;
        margin-top: 1.5%;
        left: 42.8%;
      }

      .circleLeft {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 32%;
        top: 4%;
        pointer-events: none;
      }

      .circleRight {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 57.5%;
        top: 4%;
        pointer-events: none;
      }
    }

    .rocedureImg {
      margin-top: 0.27rem;
      width: 90%;
      pointer-events: none;
    }

    button {
      border: none;
      font-weight: bold;
      font-size: 0.15rem;
      color: rgba(255, 255, 255, 1);
      width: 2rem;
      height: 0.4rem;
      /*background: linear-gradient(*/
      /*0deg,*/
      /*rgba(13, 139, 58, 1),*/
      /*rgba(46, 183, 95, 1)*/
      /*);*/
      background: url("../../../assets/image/active/gdLingNan/btn_bg.png");
      background-size: 100%;
      border-radius: 3px;
      margin-top: 0.15rem;
    }

    .downBtn {
      margin-top: 0.3rem;
    }

    .upBtn {
      margin-bottom: 0.23rem;
    }
  }

  .ruleText {
    font-size: 0.13rem;
    color: rgba(54, 54, 54, 0.8);
    width: 96%;
    padding-left: 0.2rem;

    .spot {
      width: 0.3rem;
      height: 0.3rem;
      font-weight: bold;
      color: rgba(233, 72, 86, 1);
      margin-right: 0.05rem;
    }
  }

  .last {
    margin-top: 0.2rem;
  }

  .condition {
    margin-top: 0.17rem;
    color: #ed5206;
  }

  .orage {
    padding-left: 0.29rem;
    color: #ed5206;
    margin-top: 0.03rem;
  }

  .contentText {
    padding-left: 0.3rem;
    margin-top: 0.05rem;
  }

  .time {
    margin-top: 0.1rem;
  }

  .count {
    margin-top: 0.15rem;
  }

  .price {
    margin-top: 0.15rem;
  }

  .userMessage {
    background: rgb(255, 255, 255);

    .headBox {
      height: 0.3rem;
      line-height: 0.3rem;
      position: relative;

      .headTitle {
        font-size: 0.15rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 0.9);
        text-align: center;
        margin-top: 1.5%;
        left: 42.8%;
      }

      .circleLeft {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 34%;
        top: 4%;
        pointer-events: none;
      }

      .circleRight {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 55.5%;
        top: 4%;
        pointer-events: none;
      }
    }
  }

  .fixBtn {
    position: fixed;
    bottom: 0.01rem;
    right: 0.01rem;
    width: 1.25rem;
    height: 1.27rem;
    line-height: 0.4rem;
    text-align: center;
    color: #fff;
    z-index: 999;
    background-image: url("../../../assets/image/active/openUniversity/<EMAIL>");
    background-size: 100%;
    border-radius: 10px;

    a {
      display: block;
      height: 100%;
      color: #fff;
      font-size: 21px;
      text-decoration: none;
    }
  }

  .bottomBox {
    width: 3.75rem;
    height: 0.6rem;
  }

  .popupBox {
    width: 2.83rem;
    height: 1.54rem;
    background: #fff;

    .icon {
      height: 0.32rem;
      width: 0.32rem;
      margin-top: 0.3rem;
      border-radius: 50%;
    }

    .title {
      color: #f06e6c;
      font-size: 0.14rem;
      margin-top: 0.14rem;
      line-height: 0.5rem;
    }

    .text1 {
      margin-top: 0.05rem;
    }

    .text1 {
      margin-top: 0.04rem;
      color: rgba(23, 6, 6, 0.4);
      font-size: 0.12rem;
    }
  }

  .popupBoxShare-pop {
    border-radius: 0.1rem;
  }

  .popupBoxShare {
    width: 2.83rem;
    height: 1.93rem;
    background: #fff;
    border-radius: 10px;
    text-align: center;
    border-radius: 0.1rem;
    position: relative;

    .icon {
      height: 0.32rem;
      width: 0.32rem;
      margin-top: 0.3rem;
      border-radius: 50%;
    }

    .title {
      position: absolute;
      color: rgba(23, 6, 6, 1);
      font-size: 0.14rem;
      margin-top: 0.14rem;
      bottom: 0;
      line-height: 0.5rem;
      width: 100%;
      color: #f06e6c;
      border-top: 1px solid rgba(23, 6, 6, 0.08);
    }

    .text1 {
      margin-top: 0.2rem;
      color: #170606;
      font-size: 0.14rem;
      padding: 0 0.2rem 0.2rem;
    }
  }

  .lookMore {
    background: rgb(255, 255, 255);
    padding-top: 0.06rem;
    text-align: center;
    height: 0.54rem;
    line-height: 0.54rem;
    position: relative;
    border-bottom: 1px solid rgba(54, 54, 54, 0.08);

    span {
      color: rgba(23, 6, 6, 0.4);
    }

    img {
      position: absolute;
      top: -0.03rem;
      left: 1.7rem;
      width: 0.32rem;
      height: 0.32rem;
    }
  }

  .studentStory {
    background: rgb(255, 255, 255);
    height: 2.41rem;
    width: 3.75rem;
    padding-top: 0.15rem;

    .headBox {
      height: 0.3rem;
      line-height: 0.3rem;
      position: relative;

      .headTitle {
        font-size: 0.15rem;
        font-weight: bold;
        color: rgba(54, 54, 54, 0.9);
        text-align: center;
        margin-top: 1.5%;
        left: 42.8%;
      }

      .circleLeft {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 31.5%;
        top: 4%;
        pointer-events: none;
      }

      .circleRight {
        position: absolute;
        width: 0.39rem;
        height: 0.24rem;
        left: 57.5%;
        top: 4%;
        pointer-events: none;
      }
    }
  }

  // 奖学金样式模块
  .scholarship {
    padding: 10 * @rem 12 * @rem 0 12 * @rem;

    .bonus {
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
      width: 100%;
      height: 90 * @rem;
      background: url(../../../assets/image/active/signAct/red-envelope.png);
      background-size: 100% 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;

      .top {
        display: flex;
        align-items: center;
        justify-content: center;

        span {
          font-size: 18 * @rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #fee6c2;
          line-height: 25 * @rem;
        }

        .money {
          font-size: 0.22rem;
          color: #fee6c2;
          font-weight: bold;
          margin-left: 0.1rem;
        }

        img {
          image-rendering: -moz-crisp-edges;
          image-rendering: -o-crisp-edges;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
          -ms-interpolation-mode: nearest-neighbor;
          margin: 0 5 * @rem;
          width: 63 * @rem;
          height: 21 * @rem;
        }
      }

      .bottom {
        margin-top: 11 * @rem;
        display: flex;
        align-items: center;
        justify-content: center;
        width: 84 * @rem;
        height: 24 * @rem;
        background: #f9caa9;
        border-radius: 12 * @rem;
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #8c4d0e;
        line-height: 20 * @rem;
      }
    }

    .inactivated-box {
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
      width: 100%;
      box-sizing: border-box;
      height: 0.9rem;
      // todo
      background: url(https://static.yzou.cn/zmc/a20240704ChengJian/activated-money.png) no-repeat;
      background-size: 100% 100%;
      padding-top: 19 * @rem;
      padding-bottom: 15 * @rem;
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;

      .txt-box {
        margin-left: 50 * @rem;

        span {
          margin-left: 6 * @rem;
          font-size: 18 * @rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #fee6c2;
          line-height: 25 * @rem;
        }

        .bottom {
          margin-top: 11 * @rem;
          display: flex;
          align-items: center;
          justify-content: center;
          width: 84 * @rem;
          height: 24 * @rem;
          background: #f9caa9;
          border-radius: 12 * @rem;
          font-size: 14 * @rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #8c4d0e;
          line-height: 20 * @rem;
        }
      }
    }

    .invite-group {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 14 * @rem 0;
      border-bottom: 1px solid #e9e2ce;

      .lf {
        display: flex;
        align-items: center;
        width: 70%;

        p {
          font-size: 12 * @rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #a87744;
          line-height: 17 * @rem;
        }

        img {
          image-rendering: -moz-crisp-edges;
          image-rendering: -o-crisp-edges;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;
          -ms-interpolation-mode: nearest-neighbor;
          margin-right: 8 * @rem;
          width: 32 * @rem;
          height: 32 * @rem;
        }
      }

      .rg {
        display: flex;
        justify-content: flex-end;
        width: 30%;

        button {
          width: 72 * @rem;
          height: 30 * @rem;
          background: linear-gradient(
            135deg,
            #f09190 0%,
            #f07877 66%,
            #f06e6c 100%
          );
          box-shadow: 0 * @rem 2 * @rem 5 * @rem 0 * @rem
            rgba(240, 113, 111, 0.4);
          border-radius: 20 * @rem;
          font-size: 14 * @rem;
          font-family: PingFangSC-Semibold, PingFang SC;
          font-weight: 600;
          color: #ffffff;
        }
      }
    }

    .tips-box {
      padding: 15 * @rem 0.12rem;

      h3 {
        text-align: center;
        font-size: 14 * @rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #8c4d0e;
        line-height: 14 * @rem;
        margin-bottom: 11 * @rem;
      }

      ul {
        li {
          list-style-type: square;
          color: #f99a35;
          font-size: 13 * @rem;
          margin-bottom: 3 * @rem;

          p {
            position: relative;
            font-size: 13 * @rem;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #8c4d0e;
            line-height: 17 * @rem;
          }
        }
      }
    }

    .footer-remark {
      margin-top: 24 * @rem;

      p {
        text-align: center;
        font-size: 12 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #453838;
        line-height: 17 * @rem;
      }
    }
  }
}

.van-cell {
  height: 0.54rem;
  line-height: 0.54rem;
}

.fixBottom {
  position: fixed;
  bottom: 0;
  width: 3.75rem;
  height: 0.61rem;
  z-index: 9999;
  border-top: 1px solid #fff;

  .leftBox {
    display: inline-block;
    width: 53%;
    height: 0.6rem;
    float: left;
    background: #fff;
    position: relative;

    img {
      width: 0.39rem;
      margin-left: 0.1rem;
      margin-top: 0.12rem;
    }

    span {
      display: inline-block;
    }

    .textOne {
      margin-top: 0.12rem;
      font-weight: bold;
      color: rgba(54, 54, 54, 1);
      font-size: 0.14rem;
    }

    .textTwo {
      position: absolute;
      left: 0.52rem;
      top: 0.3rem;
      font-size: 0.14rem;
      color: rgba(54, 54, 54, 0.6);
    }
  }

  .rightBox {
    display: inline-block;
    width: 47%;
    height: 0.6rem;
    background-color: rgba(251, 196, 100, 1);
    float: left;
    position: relative;

    .line {
      position: absolute;
      display: inline-block;
      width: 1px;
      height: 0.25rem;
      background: rgba(255, 255, 255, 0.4);
      top: 0.16rem;
      left: 0.88rem;
    }

    .phoneIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }

    .signUpIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        opacity: 0.8;
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(255, 255, 255, 1);
        font-size: 0.14rem;
      }
    }
  }
}

.fixBottom1 {
  position: fixed;
  bottom: 0;
  left: 0;
  height: 0.6rem;
  z-index: 9999;
  width: 100%;
  -webkit-transform: translate3d(0, 0, 0);
  transform: translateZ(0, 0, 0);

  .leftBox {
    display: inline-block;
    /*width:1.96rem;*/
    width: 55%;
    height: 0.6rem;
    float: left;
    background: #fff;
    position: relative;
    border-top: 1px solid rgba(216, 88, 86, 1);

    .pic-right {
      float: right;
      width: 0.2rem;
      height: 0.2rem;
      margin-top: 0.15rem;
      background: url("../../../assets/image/teacherKit/top.png") no-repeat 0 0;
      background-size: 100%;

      &.active {
        background: url("../../../assets/image/teacherKit/top.png") no-repeat 0
          0;
        background-size: 100%;
        transform-origin: 50% 50%;
        transform: rotate(180deg);
      }
    }

    img {
      width: 0.39rem;
      height: 0.39rem !important;
      border-radius: 50%;
      margin-left: 0.1rem;
      margin-top: 0.12rem;
    }

    span {
      width: 1.2rem;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      display: inline-block;
      line-height: 0.22rem;
    }

    .textOne {
      margin-top: 0.12rem;
      font-weight: bold;
      color: rgba(54, 54, 54, 1);
      font-size: 0.14rem;
    }

    .textTwo {
      position: absolute;
      left: 0.52rem;
      top: 0.3rem;
      font-size: 0.12rem;
      color: rgba(54, 54, 54, 0.6);
    }
  }

  .rightBox {
    display: inline-block;
    /*width: 1.79rem;*/
    width: 45%;
    height: 0.6rem;
    /*background:linear-gradient(0deg,rgba(13,139,58,1),rgba(46,183,95,1));*/
    background-color: #d7d7d8;
    background-image: linear-gradient(
      to bottom,
      rgba(225, 124, 122, 1),
      rgba(216, 88, 86, 1)
    );
    float: left;

    .phoneIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(54, 54, 54, 1);
        font-size: 0.14rem;
        color: white;
      }
    }

    .signUpIcon {
      text-align: center;
      display: inline-block;
      float: left;
      width: 50%;
      height: 100%;

      img {
        width: 0.24rem;
        margin-top: 0.1rem;
      }

      p {
        color: rgba(54, 54, 54, 1);
        font-size: 0.14rem;
        color: white;
      }
    }
  }
}

.card_wrap {
  position: fixed;
  bottom: 0.6rem;
  left: 0;
  width: 100%;
  height: 1.88rem;
  z-index: 99;
  overflow: hidden;
  padding: 0.1rem;
  background-color: #fff;
}

.card {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-image: url("../../../assets/image/teacherKit/<EMAIL>");
  background-size: 100%;
  border-radius: 0.1rem;
  position: relative;

  .talk {
    position: absolute;
    right: 0.24rem;
    top: 0.1rem;
    height: 0.2rem;
    color: white;
    font-size: 0.1rem;
  }

  .edit {
    position: absolute;
    right: 0;
    top: 0;
    width: 0.6rem;
    height: 0.6rem;
    background: url("../../../assets/image/teacherKit/<EMAIL>") no-repeat;
    background-size: 100%;
  }

  .img {
    width: 0.6rem;
    height: 0.6rem;
    border-radius: 50%;
    float: left;
    margin-right: 0.06rem;
    overflow: hidden;
    background-color: #fff;

    img {
      width: 100%;
      margin: 0;
    }
  }

  .txt {
    color: white;
    line-height: 0.24rem;
    margin: 0.3rem 0 0 0.2rem;

    h3 {
      font-weight: 500;
      font-size: 0.22rem;
      line-height: 0.3rem;
    }
  }

  .tel {
    width: 2.1rem;
    height: 0.42rem;
    float: left;
    color: white;
    margin-left: 0.2rem;
    margin-top: 0.2rem;

    span {
      &.active {
        width: 1.9rem;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }

      display: block;
      line-height: 0.22rem;

      &:first-child {
        float: left;
      }
    }

    p {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: 2.05rem;
      height: 0.25rem;
      font-size: 0.14rem;
      line-height: 0.25rem;
      overflow: hidden;

      i {
        display: inline-block;
        width: 0.12rem;
        height: 0.25rem;
        margin-left: 0.03rem;

        img {
          width: 100%;
          margin-top: 0.06rem;
        }
      }
    }
  }

  .code {
    width: 0.7rem;
    height: 0.94rem;
    float: right;
    margin-top: 0.12rem;
    margin-right: 0.3rem;
    overflow: hidden;

    /*background-color: #fff;*/
    img {
      width: 100%;
      height: 0.7rem !important;
      overflow: hidden;
    }

    span {
      display: block;
      width: 0.8rem;
      height: 0.24rem;
      color: white;
      font-size: 0.1rem;
      padding-left: 0.21rem;
      background: url("../../../assets/image/teacherKit/微信@3x.png") no-repeat
        0 -0.02rem;
      background-size: 28%;
    }
  }
}

.img_wrap {
  position: fixed;
  width: 100%;
  height: 100%;
  overflow: hidden;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  margin: auto;
  z-index: 999;

  .bg {
    position: fixed;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    z-index: 999;
  }

  img {
    width: 2.5rem;
    position: absolute;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    margin: auto;
    z-index: 99999;
  }
}

.info {
  width: 3.55rem;
  margin: 0 auto;
  margin-top: 0.4rem;

  .headBox {
    height: 0.3rem;
    line-height: 0.3rem;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .headTitle {
      font-weight: bold;
      color: rgba(54, 54, 54, 0.9);
      text-align: center;
    }

    .circleLeft {
      position: absolute;
      width: 0.39rem;
      height: 0.24rem;
      left: 29%;
      top: 4%;
      pointer-events: none;
    }

    .circleRight {
      position: absolute;
      width: 0.39rem;
      height: 0.24rem;
      left: 59.5%;
      top: 4%;
      pointer-events: none;
    }
  }

  .table {
    position: relative;
    border-radius: 0.1rem;
    background-color: #fff;
    border-left: 1px solid rgba(239, 186, 6, 1);
    border-right: 1px solid rgba(239, 186, 6, 1);
    border-top: 1px solid rgba(239, 186, 6, 1);
    overflow: hidden;

    .table-t {
      height: 0.77rem;
      background: rgba(239, 186, 6, 0.1);
      border-radius: 5px 5px 0px 0px;
      padding-left: 0.12rem;
      padding-top: 0.1rem;

      .schoolName {
        font-weight: bold;
        color: rgba(54, 54, 54, 1);
        font-size: 0.15rem;
      }

      .schoolPf {
        color: rgba(54, 54, 54, 0.8);
        font-size: 13px;
      }

      .fr {
        color: rgba(54, 54, 54, 0.8);
        font-size: 13px;
      }

      .fl,
      .fr {
        float: none;
      }
    }

    .table-b {
      table {
        width: 100%;
        border-top: 1px solid rgba(239, 186, 6, 1);

        th,
        td {
          font-size: 0.12rem;
          font-weight: 400;
          vertical-align: middle;
          border-bottom: 1px solid rgba(239, 186, 6, 1);
          border-right: 1px solid rgba(239, 186, 6, 1);
          background-color: #fff;
          padding: 0.06rem;
          text-align: center;

          &:last-of-type {
            border-right: none;
          }

          span {
            display: block;
            line-height: 0.28rem;
          }
        }

        .orage {
          color: #ed5206;
        }
      }
    }

    &:after {
      content: "";
      position: absolute;
      width: 100%;
      height: 100%;
      background-color: #ffd100;
      right: -0.07rem;
      top: -0.1rem;
      z-index: -1;
    }
  }
}

.lottery2 {
  position: fixed;
  // 隐藏
  // display: none;
  //top: {{this.showInvite?"3.08rem":"1.92rem"}};
  /*left: 2.66rem;*/
  bottom: 0.7rem;
  right: -0.37rem;
  width: 0.37rem;
  transition: all 1s;
  z-index: 99;
  /*height: 1.32rem;*/
  /*visibility: hidden;*/
}

.lottery {
  position: fixed;
  bottom: 1rem;
  right: 0.1rem;
  width: 0.87rem;
  transition: all 1s;
  z-index: 99;
}

.lotteryEntrance {
  position: fixed;
  right: 0.01rem;
  bottom: 1rem;
  width: 0.88rem;
  z-index: 9999;

  img {
    width: 100%;
  }
}

.link {
  display: flex;
  justify-content: center;
  margin-bottom: 0.15rem;
  margin-top: 0.2rem;

  .miban {
    /*margin-right: .3rem;*/
  }

  .yz,
  .miban {
    width: 1.63rem;
    height: 0.44rem;

    img {
      width: 100%;
    }
  }

  .yz2 {
    width: 1.93rem;
    height: 0.44rem;

    img {
      width: 100%;
      height: 100%;
    }
  }
}

.showf {
  display: flex;
  justify-content: space-between;
  height: 0.5rem;
  width: 100%;
  background: rgba(254, 250, 240, 1);

  div {
    width: 50%;
    height: 0.5rem;
    font-size: 0.16rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: rgba(185, 180, 180, 1);
    line-height: 0.5rem;
    text-align: center;
  }

  .active {
    background: rgba(251, 196, 100, 1);
    color: rgba(23, 6, 6, 1);
  }
}

.detail-btn {
  width: 100%;
  height: 0.9rem;
  background: white;
  padding-top: 0.27rem;

  div {
    width: 1rem;
    height: 0.36rem;
    background: #e64b23;
    text-align: center;
    line-height: 0.36rem;
    color: #fff;
    margin: 0 auto;
    font-size: 0.14rem;
    font-weight: bold;
  }
}

.postion {
  position: relative;
}

.mbInvite {
  width: 1.8rem;
  height: 0.5rem;
  // line-height: .6rem;
  margin-top: 0.12rem;
  margin: 0 auto;
  background: url("../../../assets/image/active/oneYear/btn_miban.png");
  background-size: 100% 100%;
  // color: #C13935;
  // font-size: .14rem;
  // text-align: center;
  // font-weight: bold;
}

.InvitationBg {
  width: 100%;
  height: 1.2rem;
  margin: 0.13rem 0;
  z-index: 111;

  .invitebg {
    width: 3.55rem;
    height: 1.2rem;
    margin: 0 auto;
    display: block;
  }
}

.foot-title {
  text-align: center;
  color: #453838;
  font-size: 12px;
}

.foot-top {
  margin-top: 0.32rem;
  padding-bottom: 1rem;
}

.tip-box {
  width: 3.51rem;
  height: 100%;
  background: #fff5e0;
  border-radius: 6px;
  display: flex;
  align-items: center;
  padding: 6 * @rem 2 * @rem 6 * @rem 18 * @rem;
  margin: 0 auto;
  margin-top: 0.04rem;

  .text {
    font-size: 0.13rem;
    color: #8c4d0e;
    position: relative;
  }

  .text:before {
    position: absolute;
    content: "■";
    left: -13 * @rem;
    top: 2%;
    color: #cc2725;
    font-size: 4 * @rem;
    transform: rotate(45deg);
  }
}

/deep/.van-popup {
  border-radius: 8 * @rem;
  overflow: visible;
}

// 弹框样式
.choose-box {
  position: relative;
  border-radius: 8 * @rem;
  padding: 20 * @rem 6 * @rem 24 * @rem 6 * @rem;
  width: 346 * @rem;
  height: 426 * @rem;
  background: linear-gradient(180deg, #fdca65 0%, #fff4de 100%);
  box-shadow: inset 0 * @rem 2 * @rem 4 * @rem 0 * @rem rgba(255, 255, 255, 0.5),
    inset 0 * @rem -2 * @rem 3 * @rem 0 * @rem rgba(255, 255, 255, 0.5);

  .lose-img {
    position: absolute;
    top: 0;
    right: 0;
    width: 60 * @rem;
    height: 67 * @rem;
  }

  .title {
    h3 {
      text-align: center;
      font-size: 16 * @rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      line-height: 22 * @rem;
    }

    p {
      margin-top: 20 * @rem;
      text-align: center;
      font-size: 12 * @rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #e61a16;
      line-height: 12 * @rem;
    }
  }

  .list-content {
    padding-bottom: 20 * @rem;
    margin-top: 8 * @rem;
    width: 100%;
    height: 295 * @rem;
    background: #ffffff;
    border-radius: 6 * @rem;
    overflow: auto;

    .list-item {
      margin-left: 9 * @rem;
      padding: 9 * @rem 9 * @rem 9 * @rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #eeeeee;

      .tem-tx {
        font-size: 14px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: 20px;
      }

      button {
        width: 60 * @rem;
        height: 26 * @rem;
        background: linear-gradient(
          135deg,
          #f09190 0%,
          #f07877 66%,
          #f06e6c 100%
        );
        border-radius: 14 * @rem;
        font-size: 12 * @rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ffffff;
      }
    }
  }
}

.confirm-box {
  width: 316 * @rem;
  height: 162 * @rem;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 10 * @rem;
  padding: 28 * @rem 30 * @rem 18 * @rem 30 * /*  */ @rem;

  p {
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #000000;
    line-height: 22 * @rem;
  }

  .time {
    margin: 10 * @rem 0 24 * @rem 0;
    text-align: center;
    font-size: 20 * @rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #000000;
    line-height: 20 * @rem;
  }

  .btn-box {
    display: flex;
    align-items: center;
    justify-content: space-around;

    .cancel {
      width: 120 * @rem;
      height: 40 * @rem;
      background: #ffffff;
      border-radius: 22 * @rem;
      border: 1 * @rem solid #dddddd;
      font-size: 16 * @rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #453838;
    }

    .confirm {
      width: 120 * @rem;
      height: 40 * @rem;
      background: linear-gradient(
        135deg,
        #f09190 0%,
        #f07877 66%,
        #f06e6c 100%
      );
      border-radius: 22 * @rem;
      font-size: 16 * @rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffffff;
    }
  }
}

.activation-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  box-sizing: border-box;
  height: 100;
  background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
  border-radius: 10px;
  padding: 20 * @rem 20 * @rem 18 * @rem 20 * @rem;

  h3 {
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #a44f0c;
    line-height: 22 * @rem;
  }
  .img-box {
    position: relative;
    margin-top: 20 * @rem;
    width: 270 * @rem;
    height: 83 * @rem;
    .flag {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 0.47rem;
      height: 0.18rem;
      background: #979797;
      border-radius: 0.04rem 0 0.09rem 0;
      font-size: 0.12rem;
      font-weight: 500;
      color: #fff;
      position: absolute;
      top: 0;
      left: 0;
    }

    img {
      width: 100%;
      height: 100%;
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
    }
  }

  p {
    margin-top: 18 * @rem;
    font-size: 13 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 18 * @rem;
  }

  button {
    margin-top: 28 * @rem;
    width: 276 * @rem;
    height: 40 * @rem;
    background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
    border-radius: 22 * @rem;
    font-size: 16 * @rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #ffffff;
  }
}

.rule-box {
  width: 315 * @rem;
  height: 323 * @rem;
  background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
  border-radius: 10 * @rem;
  padding: 0 15 * @rem;

  h3 {
    margin: 20 * @rem 0 15 * @rem 0;
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #a44f0c;
    line-height: 22 * @rem;
  }

  p {
    font-size: 13 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #453838;
    line-height: 18 * @rem;
  }

  .close-btn {
    position: absolute;
    bottom: -50px;
    left: 50%;
  }
}

.qrcode-box {
  width: 315 * @rem;
  height: 323 * @rem;
  background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
  border-radius: 10 * @rem;
  padding: 0 15 * @rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  p {
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 600;
    color: #453838;
    line-height: 22 * @rem;
  }

  img {
    margin-top: 30 * @rem;
    width: 99 * @rem;
    height: 99 * @rem;
  }

  .code-txt {
    margin-top: 10 * @rem;
    font-size: 13px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #e53d19;
    line-height: 18px;
  }
}

.canvas-box-all {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 335 * @rem;
  height: 485 * @rem;
  position: relative;

  .canvasImg {
    width: 100%;
    height: 100%;
    image-rendering: -moz-crisp-edges;
    image-rendering: -o-crisp-edges;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    -ms-interpolation-mode: nearest-neighbor;
  }

  p {
    position: absolute;
    bottom: -30 * @rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #fff;
    line-height: 22 * @rem;
  }
}

.canvas-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 335 * @rem;
  height: 100%;
  padding: 0.13rem 0.13rem;

  .main-box {
    position: relative;

    .canvasImg {
      width: 100%;
      height: 3.63rem;
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
    }
  }

  p {
    position: absolute;
    bottom: -35 * @rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #fff;
    line-height: 22 * @rem;
  }
}

.main-box /deep/ .van-tabs__nav--card {
  margin: 0 !important;
  height: 40px !important;
  border: none;
}

.main-box /deep/ .van-tabs__wrap {
  height: 40px !important;
}

.main-box /deep/.van-tab {
  border-right: none !important;
  color: rgba(201, 153, 103, 1) !important;
}

.main-box /deep/.van-tab--active {
  background: linear-gradient(180deg, #ff9745 0%, #ff8044 100%) !important;
  color: rgba(255, 230, 179, 1) !important;
}

.completed-box {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 335 * @rem;
  height: 485 * @rem;
  background: url(../../../assets/image/active/signAct/prove-bgc.png) no-repeat;
  background-size: 100% 100%;
  padding-top: 30 * @rem;

  h3 {
    font-size: 30 * @rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #f2ca81;
    line-height: 42 * @rem;
  }

  .avatar {
    margin-top: 20 * @rem;
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 99 * @rem;
    height: 94 * @rem;
    background: url(../../../assets/image/active/signAct/medal.png) no-repeat;
    background-size: 100% 100%;

    .head {
      width: 80 * @rem;
      height: 80 * @rem;
      box-shadow: 0 * @rem 2 * @rem 10 * @rem 0 * @rem rgba(0, 0, 0, 0.1);
      border: 2 * @rem solid #ffffff;
      border-radius: 50%;
    }

    .scarf {
      position: relative;
      top: -10px;
      width: 116 * @rem;
      height: 31 * @rem;
    }
  }

  span {
    margin-top: 10 * @rem;
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #333333;
    line-height: 22 * @rem;
  }

  p {
    margin: 4 * @rem 0 16 * @rem 0;
    font-size: 13 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #333333;
    line-height: 18 * @rem;
  }

  .img-box {
    position: relative;
    width: 300 * @rem;
    height: 92 * @rem;
    .flag {
      display: flex;
      justify-content: center;
      align-items: center;
      position: absolute;
      top: 0;
      left: 0;
      width: 0.47rem;
      height: 0.18rem;
      background: #cc2725;
      border-radius: 0.04rem 0 0.09rem 0;
      font-size: 0.12rem;
      font-weight: 500;
      color: #fff;
    }

    .coupon {
      width: 100%;
      height: 100%;
      image-rendering: -moz-crisp-edges;
      image-rendering: -o-crisp-edges;
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      -ms-interpolation-mode: nearest-neighbor;
    }
  }

  .sign-box {
    margin-top: 28 * @rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .top {
      display: flex;
      align-items: center;

      span {
        font-size: 14 * @rem;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #333333;
      }

      img {
        width: 60 * @rem;
        height: 70 * @rem;
      }
    }

    p {
      font-size: 14 * @rem;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      line-height: 20 * @rem;
    }
  }
}

.save-btn {
  width: 112 * @rem;
  height: 36 * @rem;
  background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
  border-radius: 18 * @rem;
  font-size: 14 * @rem;
  font-family: PingFangSC-Regular, PingFang SC;
  font-weight: 400;
  color: #ffffff;
  position: absolute;
  bottom: -55 * @rem;
  left: 50%;
  transform: translateX(-50%);
}

.completed-run {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 335 * @rem;
  height: 456 * @rem;
  background: #f8f7f7;
  border-radius: 4 * @rem;
  padding: 13 * @rem 13 * @rem;

  .main-box {
    width: 100%;
    height: 363 * @rem;
    background-color: #fff;

    img {
      width: 100%;
      height: 294 * @rem;
      margin-bottom: 13 * @rem;
    }

    p {
      margin-left: 11 * @rem;
      font-size: 12 * @rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #000000;
      line-height: 17 * @rem;
    }
  }

  button {
    margin-top: 24 * @rem;
    width: 112 * @rem;
    height: 36 * @rem;
    background: linear-gradient(135deg, #f09190 0%, #f07877 66%, #f06e6c 100%);
    border-radius: 18 * @rem;
    font-size: 14 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }

  .save-tips {
    text-align: center;
    width: 100%;
    position: absolute;
    bottom: -30 * @rem;
    left: 50%;
    transform: translateX(-50%);
    font-size: 14 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #ffffff;
  }
}

.title-img {
  width: 3.35rem;
  height: auto;
  margin: 0.31rem 0.2rem;

  &.charge {
    margin: 0.31rem 0.2rem 0.12rem;
  }
}

.chargetop {
  background: #fff;
  width: 3.75rem;
  height: 0.6rem;
  line-height: 0.6rem;

  img {
    width: 0.32rem;
    height: 0.32rem;
    vertical-align: middle;
    margin-left: 0.1rem;
    margin-top: -0.05rem;
  }

  p {
    display: inline-block;
    font-size: 0.17rem;
    height: 0.24rem;
    line-height: 0.24rem;
    color: #170606;
  }
}

.li-title {
  position: absolute;
  font-size: 0.14rem;
  font-family: PingFang-SC-Bold, PingFang-SC;
  font-weight: bold;
  color: #f14d25;
  left: 5%;
}
</style>
