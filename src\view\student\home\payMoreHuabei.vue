<template>
  <div class="paymore_wrap">
    <div class="paymore_bg"></div>
    <div class="paymore">
      <div class="paymore_title">
        <img src="../../../assets/image/back.png" alt=""  @click="cancel" />
        <span>请选择付款方式</span>
      </div>
      <ul>
        <li>
          <a
            href="https://openapi.alipay.com/gateway.do"
            class="J-btn-submit btn mj-submit btn-strong btn-larger btn-block"
            @click.prevent="handleHuabeiClick('HUABEI')"
          >
            <div class="paymore_m huaBei">蚂蚁花呗</div>
          </a>
        </li>
        <li>
          <!-- 该链接是拼接的请求，需要做urlEncode -->
          <div class="J-btn-submit btn mj-submit btn-strong btn-larger btn-block">
            <div class="paymore_m huaBei">花呗分期</div>
          </div>
        </li>
        <li v-for="item in installmentList" :key="item.installmentNum">
          <!-- 该链接是拼接的请求，需要做urlEncode -->
          <a
            href="https://openapi.alipay.com/gateway.do"
            class="J-btn-submit btn mj-submit btn-strong btn-larger btn-block"
            @click.prevent="
              handleHuabeiClick('HUABEI_INSTALLMENT', item.installmentNum)
            "
          >
            <div class="paymore_mh">
              <div class="paymore_mh__a">
                ￥{{ item.installmentFee }} × {{ item.installmentNum }}期
              </div>
              <div class="paymore_mh__b">手续费￥0/期（免息）</div>
            </div>
          </a>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import _AP from "../../../../static/alipay/ap";

export default {
  name: "payMoreHuabei",
  props: {
    realPayment: {
      type: String,
      default: "",
    },
  },
  data() {
    return {};
  },
  computed: {
    installmentList() {
      const arr = [];
      if (!isNaN(this.realPayment)) {
        const temp = [3, 6, 12];
        for (let index = 0; index < temp.length; index++) {
          arr.push({
            installmentNum: temp[index],
            installmentFee: Math.floor((Number(this.realPayment) / temp[index]) * 100) / 100, // 先放大100倍，再缩小100倍 , 不四舍五入
          });
        }
      }
      return arr;
    },
  },
  mounted() {},
  methods: {
    cancel() {
      this.$emit("cancle");
    },
    handleHuabeiClick(type, installmentNum) {
      // HUABEI: 花呗, COMMON: 支付宝, HUABEI_INSTALLMENT: 花呗分期
      this.$emit("alipay", type, installmentNum);
    },
  },
};
</script>

<style scoped lang="less">
.paymore_wrap {
  position: absolute;
  top: 0;
  min-height: 100vh;
  max-width: 640px;
  overflow: hidden;
  margin: 0 auto;
}
.paymore_bg {
  width: 100%;
  max-width: 640px;
  height: 100%;
  // background-color: #000;
  opacity: 0.6;
  overflow: hidden;
  position: fixed;
  top: 0;
  z-index: 2003;
}
.paymore {
  position: fixed;
  width: 100%;
  z-index: 3000;
  max-width: 640px;
  bottom: 0;
  // border-radius: 0.1rem 0.1rem 0 0;
  overflow: hidden;
  background-color: #fff;

  &_title {
    display: flex;
    align-items: center;
    width: 100%;
    height: 0.4rem;
    line-height: 0.4rem;
    font-size: 0.16rem;
    color: #333;
    border-bottom: solid 1px #f2f2f2;
    img {
      width: 0.4rem;
      height: 0.4rem;
      display: block;
    }
    span {
      flex: 1;
      text-align: center;
    }
  }
  ul {
    width: 100%;
    height: 100%;
    padding: 0 0.2rem;

    li {
      width: 100%;
      height: 0.5rem;
      a {
        display: block;
        width: 100%;
        // height: 0.54rem;
        // line-height: 0.54rem;
      }
      &:first-child {
        border-bottom: solid 1px #f2f2f2;
        line-height: 0.5rem;
      }
      &:nth-child(2) {
        line-height: 0.5rem;
      }
      .paymore_mh {
        border-top: solid 1px #f2f2f2;
        padding-top: 0.07rem;
        padding-left: 0;
        margin-left: 0.36rem;
        &__a {
          color: #333;
        }
        &__b {
          font-size: 0.12rem;
          color: #999;
        }
      }
      .paymore_m {
        padding-left: 0.36rem;
        // padding-right: 1.18rem;
      }
      .cancle {
        padding-left: 0rem;
        text-align: center;
      }
      .huaBei {
        background: url("https://static.yzou.cn/zmc/huaBei-logo.png") no-repeat
          0 center;
        background-size: 0.26rem auto;
      }
    }
  }
}
</style>
