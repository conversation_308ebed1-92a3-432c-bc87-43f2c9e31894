// 测试实际接口返回的数据格式
const testResponseBody = `'{"xinHua2026":{"key":"xinHua2026","unvsId":"168984905050474751","schoolName":"广州新华学院","bannerImg":"scholarship/xinHua2026/banner.png","couponImg":"scholarship/xinHua2026/coupon.png","logo":"scholarship/xinHua2026/logo.png","unActiveCouponImg":"scholarship/xinHua2026/un-active-coupon.png","activeCouponImg":"scholarship/xinHua2026/active-coupon.png","unActivePacket":"scholarship/xinHua2026/un-active-packet.png","activePacket":"scholarship/xinHua2026/active-packet.png","scholarshipAmountText":"3000","shareTitle":"3000元奖学金报读活动，限时参加！","latestDareOpenTime":"2026.07.17","scholarship1680":"244","scholarship599":"243","actName":"1680上进21奖学金","remark":"","majors":"[{\\\\"category\\\\": \\\\"经管类 (专升本)\\\\", \\\\"items\\\\": [{\\\\"name\\\\": [\\\\"人力资源管理\\\\", \\\\"国际经济与贸易\\\\", \\\\"会计学\\\\", \\\\"工商管理\\\\", \\\\"行政管理\\\\"], \\\\"fee\\\\": \\\\"3500.00元/学年\\\\", \\\\"miscellaneous\\\\": \\\\"400.00元/学年\\\\", \\\\"duration\\\\": \\\\"2.5年\\\\"}]}]","scholarshipRules":"test rules"}}'`

// 处理接口返回的多重转义 JSON 数据
function parseActivityConfig(responseBody, activityKey) {
  try {
    console.log('原始响应数据长度:', responseBody.length)
    
    // 移除外层多余的引号
    let cleanedData = responseBody
    if (cleanedData.startsWith("'") && cleanedData.endsWith("'")) {
      cleanedData = cleanedData.slice(1, -1)
    }
    if (cleanedData.startsWith('"') && cleanedData.endsWith('"')) {
      cleanedData = cleanedData.slice(1, -1)
    }
    
    console.log('移除外层引号后长度:', cleanedData.length)
    
    // 处理转义字符
    cleanedData = cleanedData.replace(/\\"/g, '"')
    
    console.log('处理转义字符后长度:', cleanedData.length)
    
    // 解析主 JSON 对象
    const allConfigs = JSON.parse(cleanedData)
    console.log('所有活动配置:', Object.keys(allConfigs))
    
    // 获取指定活动的配置
    const activityConfig = allConfigs[activityKey]
    if (!activityConfig) {
      throw new Error(`未找到活动配置: ${activityKey}`)
    }
    
    console.log('找到活动配置:', activityKey)
    
    // 处理 majors 字段（它被序列化成了字符串）
    if (typeof activityConfig.majors === 'string') {
      try {
        console.log('原始 majors 字符串长度:', activityConfig.majors.length)
        
        // 处理多重转义的 majors 字符串
        let majorsStr = activityConfig.majors
        majorsStr = majorsStr.replace(/\\\\/g, '\\') // 处理双反斜杠
        majorsStr = majorsStr.replace(/\\"/g, '"')   // 处理转义引号
        
        console.log('处理转义后的 majors 长度:', majorsStr.length)
        
        activityConfig.majors = JSON.parse(majorsStr)
        console.log('解析后的 majors 数组长度:', activityConfig.majors.length)
      } catch (majorsError) {
        console.error('解析 majors 字段失败:', majorsError)
        activityConfig.majors = []
      }
    }
    
    // 处理其他可能被转义的 HTML 字段
    if (activityConfig.scholarshipRules) {
      activityConfig.scholarshipRules = activityConfig.scholarshipRules
        .replace(/\\n/g, '\n')
        .replace(/\\"/g, '"')
    }
    
    if (activityConfig.rules) {
      activityConfig.rules = activityConfig.rules
        .replace(/\\n/g, '\n')
        .replace(/\\"/g, '"')
    }
    
    return activityConfig
  } catch (error) {
    console.error('解析活动配置失败:', error)
    throw error
  }
}

// 测试
console.log('=== 测试解析实际接口数据 ===')
try {
  const result = parseActivityConfig(testResponseBody, 'xinHua2026')
  console.log('\n✅ 解析成功！')
  console.log('学校名称:', result.schoolName)
  console.log('专业数量:', result.majors.length)
  console.log('第一个专业类别:', result.majors[0]?.category)
} catch (error) {
  console.error('\n❌ 解析失败:', error.message)
}
