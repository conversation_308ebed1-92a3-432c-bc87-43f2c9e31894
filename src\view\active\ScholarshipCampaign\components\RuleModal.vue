<template>
  <!-- 规则说明弹框 -->
  <van-popup round v-model="visible">
    <div class="rule-box">
      <h3>奖学金规则</h3>
      <div v-html="scholarshipRules"></div>
    </div>
    <van-icon
      @click="handleClose"
      style="
        position: absolute;
        bottom: -50px;
        left: 50%;
        transform: translateX(-50%);
      "
      name="close"
      size="40"
      color="#ffffff"
    />
  </van-popup>
</template>

<script>
export default {
  name: 'RuleModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    scholarshipRules: {
      type: String,
      default: ''
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleClose() {
      this.$emit('close')
    }
  }
}
</script>

<style lang="less" scoped>
@rem: 0.01rem;

.rule-box {
  width: 315 * @rem;
  max-height: 50vh;
  background: linear-gradient(180deg, #fdfcfa 0%, #fbf9f6 100%);
  border-radius: 10 * @rem;
  padding: 0 15 * @rem 15 * @rem;
  overflow: scroll;

  h3 {
    margin: 20 * @rem 0 15 * @rem 0;
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #a44f0c;
    line-height: 22 * @rem;
  }

  /deep/ p {
    font-size: 13 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #453838;
    line-height: 18 * @rem;
  }

  .close-btn {
    position: absolute;
    bottom: -50px;
    left: 50%;
  }
}
</style>
