<template>
  <!-- 选择新一轮挑战 -->
  <van-popup v-model="visible" :close-on-click-overlay="false">
    <div class="choose-box">
      <img
        class="lose-img"
        src="../../../../assets/image/active/signAct/lose.png"
        alt
      />
      <div class="title">
        <h3>很遗憾，您的上一轮挑战失败</h3>
        <h3>请选择新的挑战时间</h3>
        <p>
          *当前剩{{ remainChallengeCount }}次挑战机会截止在{{
            latestDareOpenTime
          }}日前可用
        </p>
      </div>
      <div class="list-content">
        <div
          class="list-item"
          v-for="(item, index) in recordList"
          :key="index"
        >
          <div class="item-txt">
            {{ index + 1 }}.打卡开始时间：{{
              item.attendTime | formatDate("yyyy.MM.dd")
            }}
          </div>
          <button @click="handleSelectChallenge(item.semesterId, item.attendTime)">
            选这期
          </button>
        </div>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'ChallengeSelectionModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    recordList: {
      type: Array,
      default: () => []
    },
    remainChallengeCount: {
      type: Number,
      default: 0
    },
    latestDareOpenTime: {
      type: String,
      default: ''
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleSelectChallenge(semesterId, attendTime) {
      this.$emit('select-challenge', { semesterId, attendTime })
    }
  }
}
</script>

<style lang="less" scoped>
@rem: 0.01rem;

.choose-box {
  position: relative;
  border-radius: 8 * @rem;
  padding: 20 * @rem 6 * @rem 24 * @rem 6 * @rem;
  width: 346 * @rem;
  height: 426 * @rem;
  background: linear-gradient(180deg, #fdca65 0%, #fff4de 100%);
  box-shadow: inset 0 * @rem 2 * @rem 4 * @rem 0 * @rem rgba(255, 255, 255, 0.5),
    inset 0 * @rem -2 * @rem 3 * @rem 0 * @rem rgba(255, 255, 255, 0.5);

  .lose-img {
    position: absolute;
    top: 0;
    right: 0;
    width: 60 * @rem;
    height: 67 * @rem;
  }

  .title {
    h3 {
      text-align: center;
      font-size: 16 * @rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #333333;
      line-height: 22 * @rem;
    }

    p {
      margin-top: 20 * @rem;
      text-align: center;
      font-size: 12 * @rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #e61a16;
      line-height: 12 * @rem;
    }
  }

  .list-content {
    padding-bottom: 20 * @rem;
    margin-top: 8 * @rem;
    width: 100%;
    height: 295 * @rem;
    background: #ffffff;
    border-radius: 6 * @rem;
    overflow: auto;

    .list-item {
      margin-left: 9 * @rem;
      padding: 9 * @rem 9 * @rem 9 * @rem 0;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #eeeeee;

      .item-txt {
        font-size: 14px;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #333333;
        line-height: 20px;
      }

      button {
        width: 60 * @rem;
        height: 26 * @rem;
        background: linear-gradient(
          135deg,
          #f09190 0%,
          #f07877 66%,
          #f06e6c 100%
        );
        border-radius: 14 * @rem;
        font-size: 12 * @rem;
        font-family: PingFangSC-Semibold, PingFang SC;
        font-weight: 600;
        color: #ffffff;
        border: none;
      }
    }
  }
}
</style>
