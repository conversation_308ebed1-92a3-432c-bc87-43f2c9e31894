<template>
  <div class="transitPage-wrapper" @click="toActivePage">
    <img src="./img/transitImage.png" alt />
  </div>
</template>
<script>
export default {
  data() {
    return {
      url: ''
    }
  },
  created() {
    this.toActivePage()
  },
  methods: {
    getLink() {
      const inviteToken = this.$route.query.token
        ? this.$route.query.token.replace(/\ +/g, '+')
        : ''
      const id = this.$route.query.id
      const type = this.$route.query.type
      const regOrigin = this.$route.query.regOrigin
      const regChanel = this.$route.query.regChanel
      const data = {
        appletsUrl: '/pages/other/news/index',
        query: `inviteToken=${inviteToken}&regChanel=${regChanel}&circleId=${id}&type=${type}&regOrigin=${regOrigin}`
      }
      return this.$http.post('/us/getAppletsToLink/1.0/', data)
    },
    async toActivePage() {
      const res = await this.getLink()
      if (res.body) {
        location.href = res.body
      }
    }
  }
}
</script>
<style lang="less" scoped>
.transitPage-wrapper {
  width: 100vw;
  height: 100vh;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  overflow: hidden;
  img {
    width: 100%;
    height: 100%;
  }
}
</style>
