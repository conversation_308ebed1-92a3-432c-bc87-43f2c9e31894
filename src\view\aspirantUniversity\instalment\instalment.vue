<template>
  <div class="main-wrap">
    <yz-steps
      class="mb10"
      :stepNames="isShowStep"
      :current="mStaData[mInstallmentConfig.installmentStatus]"
    />
    <div
      v-if="mInstallmentConfig.installmentStatus === 'WAIT_DOWN_PAYMENT'"
      class="shadow-block p15"
    >
      <h2 class="block-title mb10">订单信息</h2>
      <div class="cell mb10">
        <span class="sub-cell">课程总额</span>
        <span class="sub-cell text-right">
          {{ mInstallmentConfig.courseAmount || 0 }}元
        </span>
      </div>
      <div class="cell mb10">
        <span class="sub-cell">首付</span>
        <span class="sub-cell text-right"
          >{{ mInstallmentConfig.downPaymentAmount || 0 }}元</span
        >
      </div>
      <div class="cell mb10">
        <span class="sub-cell">优惠/抵扣</span>
        <span class="sub-cell text-right">
          {{ mInstallmentConfig.scaleAmount || 0 }}元
        </span>
      </div>
      <div class="cell mb10">
        <span class="sub-cell">贷款总额</span>
        <span class="sub-cell text-right">
          {{ mInstallmentConfig.installmentAmount || 0 }}元
        </span>
      </div>
    </div>
    <div class="loan-cycle-box">
      <h3 class="title">选择贷款期数</h3>
      <div class="loan-time">
        <div
          v-for="item in installmentConfigs"
          :key="item.id"
          :class="[{ select: stagingNum === item.period }, 'loan-time-box']"
          @click="selectCycle(item)"
        >
          {{ item.period }}个月
        </div>
      </div>
      <div class="interest-rate-box">
        <div class="flex1">
          <p class="test" :class="{ 'rate-between': stagingNum }">
            <span>贷款利率</span><span v-if="stagingNum">{{ stagingInterest }}%</span>
          </p>
          <span v-if="!stagingNum" class="placeholder">请选择期数</span>
        </div>
        <p
          v-if="subsidyProportion > 0"
          :class="{ 'rate-between': subsidyProportion }"
          style="margin-top: 0.1rem;"
        >
          <span>贴息比例</span>
          <span>{{ parseFloat(subsidyProportion) }}%</span>
        </p>
      </div>
      <div class="interest-plan flex1">
        <p>还款计划</p>
        <div class="interest-plan-r flex1" @click="handleDetailDialog">
          <div>
            <p :style="{ color: isDisabled ? '#cccccc' : '#4aa3f2' }">
              总利息 ¥{{ totalInterest }}<span v-if="subsidyType !== 1">(已贴息 ¥ {{ subsidyTotal }})</span>
            </p>
            <p :style="{ color: isDisabled ? '#cccccc' : '#4aa3f2' }">
              点击查看每月还款计划
            </p>
          </div>
          <img src="../../../assets/image/student/right-icon.png" alt="" />
        </div>
      </div>
      <div class="tips">
        贷款利率及还款利息最终请以海尔消费金融贷款页面为准，本页面提供的贷款信息仅供参考预览
      </div>
    </div>
    <div
      v-if="mInstallmentConfig.installmentStatus !== 'WAIT_DOWN_PAYMENT'"
      class="user-info"
    >
      <div class="user-info-head flex1">
        <h3 class="title">核对个人信息</h3>
        <div v-if="!isDisabled" class="flex1 mb10" @click="editTel">
          <span>修改</span>
          <img src="../../../assets/image/student/edit-icon.png" alt="" />
        </div>
      </div>
      <div class="user-info-connect">
        <div class="info-item">
          <span>真实姓名：</span>
          <span>{{ mInstallmentConfig.applyName }}</span>
        </div>
        <div class="info-item">
          <span>手机号码：</span>
          <span>{{ mInstallmentConfig.mobile }}</span>
        </div>
        <div class="info-item">
          <span>身份证号：</span>
          <span>{{ mInstallmentConfig.idCard }}</span>
        </div>
        <div class="tips">
         {{ mInstallmentConfig.installmentStatus == 'INSTALLMENT_ING' ? '若需要修改个人信息，请联系助学老师修改。' : '请填写有效的真实姓名、手机号、身份证，如果信息不正确将无法进行分期。' }} 
        </div>
      </div>
    </div>
    <div
      v-if="mInstallmentConfig.installmentStatus === 'WAIT_DOWN_PAYMENT'"
      class="shadow-block question p15 mb10"
    >
      <h2 class="block-title">贷款常见问题</h2>
      <div class="item">
        <p class="topic">是在哪个平台上贷的款？</p>
        <p class="answer">贷款服务全程由海尔消费金融提供</p>
      </div>
      <div class="item">
        <p class="topic">借款有哪些申请条件？</p>
        <p class="answer">
          20周岁-55周岁之间的中国大陆居民、征信良好，有银行借记卡。
        </p>
      </div>
      <div class="item">
        <p class="topic">贷款会上报征信吗？</p>
        <p class="answer">根据中国人民银行规定，贷款会上报征信系统。</p>
      </div>
      <div class="item">
        <p class="topic">提前还款需要违约金吗？</p>
        <p class="answer" style="color: #f17674">
          若提前还款，不需要承担违约金。
        </p>
      </div>
      <div class="item">
        <p class="topic">借款失败怎么办？</p>
        <p class="answer">
          申请额度或者操作借款审核失败/暂不能提供服务是因为综合评估未通过，综合评估和经济环境、政策调整，以及您的信用、
          职业收入、借还款情况等相关，目前审核结果人工也无法干预哦
        </p>
      </div>
    </div>

    <div class="loan-footer">
      <!-- <bottom-btns
        class="first-play"
        v-if="mInstallmentConfig.installmentStatus === 1 && !showCancel"
        isOnlyOne="right"
        :rightBtnText="`立即支付首付${mInstallmentConfig.downPaymentAmount}元`"
        @rightClick="submit" /> -->
      <bottom-btns
        v-if="mInstallmentConfig.installmentStatus == 'WAIT_DOWN_PAYMENT'"
        isleftPlain
        leftBtnText="取消订单"
        @leftClick="handleCancel"
        :rightBtnText="`立即支付首付${mInstallmentConfig.downPaymentAmount}元`"
        @rightClick="toPay"
      />
      <bottom-btns
        v-if="mInstallmentConfig.installmentStatus == 'WAIT_PERFECT_INFO'"
         isOnlyOne="right"
        rightBtnText="申请额度"
        @rightClick="handleNextApplyStaged"
      />
      <bottom-btns
        v-if="mInstallmentConfig.installmentStatus == 'INSTALLMENT_ING'"
        isOnlyOne="right"
        rightBtnText="去查看分期操作"
        :leftDisabled="!mInstallmentConfig.supportUpdateStatus"
        @rightClick="handleToReadStagedInfo"
      />
    </div>
    <van-action-sheet v-model="asShow" title="还款计划">
      <div class="as-content">
        <div class="info">
          <div class="item">
            <span class="label">还款方式：</span>
            <strong>等本等息</strong>
          </div>
          <div class="item">
            <span class="label">贷款金额：</span>
            <span class="red">¥ {{ mInstallmentConfig.installmentAmount || 0 }}</span>
          </div>
          <div class="item">
            <span class="label">总利息：</span>
            <span class="red">¥ {{ totalInterest }}</span><span v-if="subsidyType !== 1">(已贴息 ¥ {{ subsidyTotal }})</span>
          </div>
        </div>
        <table class="table">
          <thead>
            <th></th>
            <th>本金</th>
            <th>利息</th>
            <th>应还</th>
          </thead>
          <tbody>
            <tr v-for="(item,index) in paymentList" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ parseFloat((item.principal)) }}</td>
              <td>{{ parseFloat((item.actualInterest)) }}</td>
              <td>{{ parseFloat((item.totalPayment)) }}</td>
            </tr>
            <tr>
              <td>总计</td>
              <td>{{ mInstallmentConfig.installmentAmount || 0}}</td>
              <td>{{ totalInterest }}</td>
              <td>{{ parseFloat((mInstallmentConfig.installmentAmount + totalInterest).toFixed(2)) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </van-action-sheet>
    <van-popup
      class="pop"
      v-model="showPop"
      round
      position="bottom"
      :style="{ height: '30%' }"
    >
      <div class="untils flex1">
        <span @click="showPop = false">取消</span>
        <span
          :style="{
            color:
              mTempUserInfo.applyName &&
              mTempUserInfo.mobile &&
              mTempUserInfo.idCard &&
              'red',
          }"
          @click="handleSaveInfo"
          >保存</span
        >
      </div>
      <van-field
        v-model="mTempUserInfo.applyName"
        autofocus
        ref="getFocus"
        label="修改真实姓名"
        placeholder="请输入真实姓名"
      />
      <van-field
        v-model="mTempUserInfo.mobile"
        type="tel"
        label="修改手机号码"
        placeholder="请输入手机号"
      />
      <van-field
        v-model="mTempUserInfo.idCard"
        type="tel"
        label="修改身份证号"
        placeholder="请输入身份证号"
      />
    </van-popup>
  </div>
</template>

<script>
import { Dialog, Toast } from "vant";
import yzSteps from "@/components/yz-steps";
import bottomBtns from "@/components/basic/bottom-btns";
import { isIDCard } from "@/common";
import { calculateRepayment, calculateEqualInstallmentRate } from '@/utils/loanCalculator'

export default {
  components: { yzSteps, bottomBtns },
  data() {
    return {
      paymentList: [],
      isShowStep: ["支付首付", "海尔消费金融贷款", "完成支付"],
      qrCodeUrl: "",
      beforeInterestSum: 0, //过去应还本金的累计

      stagingNum: 6, // 分期数 6期：0.9%, 12期:0.9%
      stagingInterest: 0.9,
      showCancel: false,
      asShow: false,
      showPop: false,
      mInstallmentConfig: {},
      mStaData: {
        WAIT_DOWN_PAYMENT: 1,
        WAIT_PERFECT_INFO: 2,
        INSTALLMENT_ING: 2,
        INSTALLMENT_COMPLETE: 3,
      },
      mTempUserInfo: {},
      mInstallmentDetail: {},
      mCheckCurrentMonthDetail: null,
      mCanEditUserInfo: false,
      mParams: {
        installmentId: this.$route.query.installmentId,
      },
      installmentConfigs: [],
      yearRate:18.2839, //年利率
      totalInterest:0,//总利息
      subsidyProportion: 0, // 贴息比例
      subsidyType: 1, // 1 客息 2混合 3 全贴,
      subsidyTotal: 0, // 总贴息
      stagingInterestYear: 0, // 海尔消费金融
    };
  },
  async created() {
    await this.getOrderInfo();
  },
  computed: {
    isDisabled() {
      return ["INSTALLMENT_ING", "INSTALLMENT_COMPLETE"].includes(
        this.mInstallmentConfig.installmentStatus
      );
    },
  },
  methods: {
    // 获取订单信息
    getOrderInfo() {
      this.$http
        .post("/pu/getInstallmentInfo/2.0/", this.mParams)
        .then((res) => {
          let { code, body, message } = res;
          if (code === "00") {
            this.mInstallmentConfig = body;
            this.mTempUserInfo = { ...body };
            this.installmentConfigs = body?.installmentConfigs;
            const item = this.installmentConfigs[0] || {}
            this.selectCycle(item, 1)
          } else {
            Toast(message);
          }
        });
    },
    selectCycle(obj, typs = 0) {
      if (this.isDisabled && typs === 0) return;
      console.log(obj, 'this.obj')
      const { period, subsidyProportion, subsidyType } = obj
      let actualInterestRate = obj?.interestRate
      this.stagingInterestYear = actualInterestRate;
      this.subsidyType = subsidyType
      this.mCheckCurrentMonthDetail = this.mInstallmentDetail[period];
      this.stagingNum = Number(period)
      this.stagingInterest = Number(((actualInterestRate / period) * 100).toFixed(2))
      this.subsidyProportion = Number((subsidyProportion).toFixed(2))
      console.log(this.subsidyProportion, this.subsidyProportion == 100 ,'this.subsidyProportion')
      // 计算总利息
      this.subsidyTotal = 0
      this.paymentList = [];
      const { installmentAmount, repaymentType } = this.mInstallmentConfig
      let RepaymentMethod  = repaymentType === 'EQUAL_PRINCIPAL_AND_EQUAL_INTEREST'?'EQUAL_PRINCIPAL' :'EQUAL_PRINCIPAL_INTEREST'
      // 学易的特殊处理下，贴息改为等本等息
      if (this.subsidyProportion == 100) {
        console.log('等本等息---')
        RepaymentMethod = 'EQUAL_PRINCIPAL'
      }
      console.log(this.mInstallmentConfig, repaymentType, 'as祁同伟///////******')
      //实际年化利率
      if (RepaymentMethod === 'EQUAL_PRINCIPAL_INTEREST') {
        console.log((installmentAmount * (1 + actualInterestRate)) / period, '2----installmentConfig')
        actualInterestRate = calculateEqualInstallmentRate(period, (installmentAmount * (1 + actualInterestRate)) / period, installmentAmount) * period
      }
      const  { totalActualInterest, totalSubsidy, details} = calculateRepayment(
        installmentAmount,
        actualInterestRate * 100,
        period,
        RepaymentMethod,
        this.subsidyProportion,
        obj?.interestRate * 100
      );
      // 展示还款计划
      this.totalInterest = totalActualInterest
      this.subsidyTotal = totalSubsidy
      this.paymentList = details
    },
    handleDetailDialog() {
      this.asShow = true;
    },
    handleChangeSelfInfo() {
      this.$router.push({
        name: "aspirantUpdataUserInfo",
        query: this.mParams,
      });
    },
    handleToReadStagedInfo() {
      this.toLastApplyPage();
    },
    // 取消订单
    handleCancel() {
      Dialog.confirm({
        title: "确认要取消订单吗？",
        message: "订单取消后, 你还可以选择其他支付方式",
        confirmButtonColor: "#F06E6C",
        confirmButtonText: "确认取消",
        cancelButtonText: "再考虑一下",
      })
        .then(async () => {
          const res = await this.$http.post(
            "/pu/cancelInstallment/2.0/",
            this.mParams
          );
          if (res.code === "00") {
            Toast.success("取消成功");
            this.$router.go(-1);
            // setTimeout(() => {
            //   this.$router.replace('/student')
            // }, 200)
          }
        })
        .catch(() => {});
    },
    // 去分期
    async goStgPay() {
      if (!this.stagingNum) {
        Toast("请选择贷款期数");
        return;
      }
      this.mInstallmentConfig.installmentStatus = 3;
    },
    // 提交首付订单
    toPay() {
      this.$router.push({
        name: "aspirantInstalmentDownPayment",
        query: {
          ...this.mParams,
          stagingNum: this.stagingNum,
          installmentType: 39
        },
      });
    },
    editTel() {
      if (this.isDisabled) return;
      this.showPop = true;
    },
    async handleSaveInfo() {
      const { applyName, mobile, idCard } = this.mTempUserInfo;

      if (!applyName || applyName.length > 15) {
        Toast("姓名有误");
        return;
      }
      const r = /^1\d{10}$/;
      if (!r.test(mobile)) {
        Toast("手机号有误");
        return;
      }
      if (!isIDCard(idCard)) {
        Toast("身份证有误");
        return;
      }
      Object.assign(this.mInstallmentConfig, this.mTempUserInfo);
      this.showPop = false;
    },
    // 下一步 申请分期
    async handleNextApplyStaged() {
      const { applyName, mobile, idCard, installmentId } =
        this.mInstallmentConfig;
      if (!applyName || !mobile || !idCard) {
        Toast("请先完善个人信息，再申请分期");
        return;
      }
      //   this.mInstallmentConfig.installmentStatus = 'INSTALLMENT_ING'
      // return
      let params = {
        installmentId,
        installmentNum: this.stagingNum,
        applyName,
        mobile,
        idCard,
      };
      const res = await this.$http.post(
        "/pu/perfectInstallmentInfo/2.0/",
        params
      );
      if (res.code === "00") {
        this.toLastApplyPage();
      }
    },
    toLastApplyPage() {
      this.$router.push({
        name: "aspirantStagedLastApply",
        query: {
          ...this.mParams,
          installmentType: 39
        },
       
      });
    },
  },
};
</script>

<style lang="less" scoped>
@import "../../../assets/less/variable.less";
/* * { touch-action: pan-y; } */
.loan-footer /deep/ .complete-bottom-btns button:nth-child(1) {
  border-color: #a29b9bff;
  font-weight: 400;
  color: #746a6aff;
  flex: 1.5;
}

.loan-footer /deep/ .first-play button:nth-child(1) {
  color: #ffffff;
}
.loan-footer /deep/ .complete-bottom-btns button:nth-child(2) {
  flex: auto;
}
.pop /deep/ .van-field__label {
  width: 0.1.2rem;
}
.pop /deep/ .van-field__label {
  width: 8.2em;
}
.pop /deep/ .van-field__label span {
  color: #453838ff;
}
.flex1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title {
  margin-bottom: 0.1rem;
  font-size: 0.15rem;
  font-weight: 600;
  line-height: 0.21rem;
}
.tips {
  font-size: 0.12rem;
  color: #7d7d7d;
  line-height: 0.17rem;
}
.mb10 {
  margin-bottom: 10px;
}
.main-wrap {
  margin-bottom: 0.5rem;
  .yz-steps {
    padding: 0.25rem 0.1rem 0.3rem;
    width: 100%;
    height: 0.85rem;
    display: flex;
    /* margin-left: -.1rem; */
    /deep/.part {
      width: 0.5rem;
      flex: 1;
    }
  }
  .loan-cycle-box {
    margin: 0.15rem auto;
    padding: 0.12rem 0.15rem;
    width: 3.35rem;
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.08rem 0rem rgba(0, 0, 0, 0.1);
    border-radius: 0.05rem;

    .loan-time {
      display: flex;
      justify-content: space-between;
      .loan-time-box,
      .select {
        width: 1.38rem;
        height: 0.42rem;
        line-height: 0.42rem;
        text-align: center;
        font-size: 0.2rem;
        font-weight: 400;
        color: #999999;
        border-radius: 0.05rem;
        border: 0.01rem solid #cdcdcd;
      }
      .select {
        position: relative;
        background: rgba(240, 110, 108, 0.1);
        border-radius: 0.05rem;
        color: #f06e6c;
        border: 0.01rem solid #f06e6c;
        &::after {
          position: absolute;
          content: "";
          display: block;
          width: 0.25rem;
          height: 0.25rem;
          bottom: -0.01rem;
          right: -0.01rem;
          background: url("../../../assets/image/student/select.png") no-repeat;
          background-size: 100% 100%;
        }
      }
    }

    .interest-rate-box,
    .interest-plan {
      padding: 0.22rem 0 0.13rem;
      border-bottom: 0.01rem solid #ebebeb;
      p {
        font-size: 0.14rem;
        color: #453838;
      }

      .placeholder {
        color: #cdcccc;
      }

      .tip {
        color: red;
      }

      .interest-rate-txt {
        color: #000;
      }
    }
    .interest-plan {
      border: none;
      align-items: center;
      font-size: 0.12rem;
      color: #4aa3f2;
      .interest-plan-r p {
        text-align: right;
        line-height: 0.17rem;
      }
      img {
        width: 0.32rem;
        height: 0.32rem;
      }
    }
  }
  .user-info {
    margin: 0 auto;
    width: 3.35rem;
    padding: 0.16rem 0.15rem;
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.08rem 0rem rgba(0, 0, 0, 0.1);
    border-radius: 0.05rem;
    .user-info-head {
      margin-bottom: 0.1rem;
      border-bottom: 0.01rem solid #ebebeb;
      span {
        color: #f06e6cff;
        margin-right: 0.05rem;
      }
      img {
        width: 0.14rem;
        height: 0.14rem;
      }
    }
    .user-info-connect {
      .info-item {
        margin-bottom: 0.08rem;
        span {
          font-size: 0.14rem;
          color: #666666ff;
          line-height: 0.2rem;
          &:nth-child(2) {
            color: #453838ff;
          }
        }
      }
      .tips {
        color: rgb(242, 24, 24);
      }
    }
  }
  .pay-box {
    margin: 0.15rem auto 0;
    padding: 0.36rem 0.4rem 0.27rem;
    width: 3.35rem;
    height: 3.8rem;
    background: #ffffff;
    box-shadow: 0rem 0.02rem 0.08rem 0rem rgba(0, 0, 0, 0.1);
    border-radius: 0.05rem;
    h3 {
      text-align: center;
      font-size: 0.18rem;
      font-weight: 600;
      color: #000000;
      line-height: 0.25rem;
      margin-bottom: 0.1rem;
    }
    .tip-txt {
      text-align: center;
      font-size: 0.14rem;
      color: #999999;
      line-height: 0.2rem;
    }
    .step-box {
      span {
        margin-bottom: 0.1rem;
        padding-top: 0.1rem;
        display: block;
        color: #999999;
      }
      p {
        margin-bottom: 0.05rem;
        font-size: 0.14rem;
        color: #333333;
      }
    }
    .eqcode-box {
      margin: 0.24rem auto;
      text-align: center;
      width: 2.35rem;
      height: 0.65rem;
      line-height: 0.65rem;
      background: linear-gradient(180deg, #ef8371 0%, #db4c3e 100%);
      border-radius: 0.05rem;
      font-size: 0.18rem;
      font-weight: 600;
      color: #ffffff;
    }
  }
  .cell {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: #453838;
    font-size: 14px;
    .sub-cell {
      /* flex: 1; */
    }
  }
  .shadow-block {
    width: 3.35rem;
    margin: 0 0.2rem;
    background: #ffffff;
    box-shadow: 0 0.02rem 0.1rem 0 rgba(0, 0, 0, 0.1);
    border-radius: 0.04rem;

    .block-title {
      font-size: 0.15rem;
      font-weight: 600;
      color: #000000;
      padding-bottom: 0.1rem;
      border-bottom: 1px solid #f8f8f8;
    }

    .h2 {
      font-size: 0.15rem;
      font-weight: 600;
      color: #000000;
    }

    .rema {
      margin-top: 0.05rem;
      font-size: 0.12rem;
      font-weight: 400;
      color: #7d7d7d;
    }
  }
  .question {
    margin-top: 0.15rem;
    margin-bottom: 0.8rem;
    .item {
      margin-top: 0.1rem;
    }

    .topic {
      font-size: 0.14rem;
      font-weight: 400;
      color: #000000;
    }

    .answer {
      font-size: 0.13rem;
      color: #7d7d7d;
    }
  }
  .p15 {
    padding: 0.15rem;
  }
}
.pop {
  padding: 0.2rem 0 0rem;
  .untils {
    padding: 0 0.16rem;
  }
  .untils {
    margin-bottom: 0.1rem;
    /* span:nth-child(2) {
      color: red;
    } */
  }
}
.as-content {
  padding: 0 0.2rem 0.3rem 0.2rem;

  .info {
    .item {
      margin-top: 0.08rem;
      font-size: 0.14rem;
      .label {
        width: 0.8rem;
        margin-right: 0.2rem;
        display: inline-block;
        color: #9b9595;
      }
    }
  }

  .table {
    width: 3.35rem;
    margin-top: 0.15rem;

    th,
    td {
      border: 1px solid #ebebeb;
      text-align: center;
      padding: 0.06rem 0;
    }
  }
}
</style>
