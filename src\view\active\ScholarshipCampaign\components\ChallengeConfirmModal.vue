<template>
  <!-- 确认选择哪个挑战 -->
  <van-popup round v-model="visible">
    <div class="confirm-box">
      <p>确定选择下一次挑战开始时间：</p>
      <p class="time">{{ newTime | formatDate("yyyy.MM.dd") }}</p>
      <div class="btn-box">
        <button class="cancel" @click="handleCancel">返回</button>
        <button class="confirm" @click="handleConfirm">确定</button>
      </div>
    </div>
  </van-popup>
</template>

<script>
export default {
  name: 'ChallengeConfirmModal',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    newTime: {
      type: [String, Number, Date],
      default: null
    }
  },
  computed: {
    visible: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  methods: {
    handleCancel() {
      this.$emit('cancel')
    },
    handleConfirm() {
      this.$emit('confirm')
    }
  }
}
</script>

<style lang="less" scoped>
@rem: 0.01rem;

.confirm-box {
  width: 316 * @rem;
  height: 162 * @rem;
  box-sizing: border-box;
  background: #ffffff;
  border-radius: 10 * @rem;
  padding: 28 * @rem 30 * @rem 18 * @rem 30 * @rem;

  p {
    text-align: center;
    font-size: 16 * @rem;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 400;
    color: #000000;
    line-height: 22 * @rem;
  }

  .time {
    margin: 10 * @rem 0 24 * @rem 0;
    text-align: center;
    font-size: 20 * @rem;
    font-family: PingFangSC-Semibold, PingFang SC;
    font-weight: 600;
    color: #000000;
    line-height: 20 * @rem;
  }

  .btn-box {
    display: flex;
    align-items: center;
    justify-content: space-around;

    .cancel {
      width: 120 * @rem;
      height: 40 * @rem;
      background: #ffffff;
      border-radius: 22 * @rem;
      border: 1 * @rem solid #dddddd;
      font-size: 16 * @rem;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 400;
      color: #453838;
    }

    .confirm {
      width: 120 * @rem;
      height: 40 * @rem;
      background: linear-gradient(
        135deg,
        #f09190 0%,
        #f07877 66%,
        #f06e6c 100%
      );
      border-radius: 22 * @rem;
      border: none;
      font-size: 16 * @rem;
      font-family: PingFangSC-Semibold, PingFang SC;
      font-weight: 600;
      color: #ffffff;
    }
  }
}
</style>
