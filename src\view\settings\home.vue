<template>
  <div>
    <div class="invite" v-if="showInvite">
          <img :src="invite.headImg?invite.headImg+headLimit:invite.headImg|defaultAvatar" alt="">
          <div class="rightView">
              <p>您的好友 <span>{{invite.nickName}}</span></p>
              <p>邀请您一起来提升学历</p>
          </div>
      </div>
    <div class="settings-top">
      <div class="settings-top-t">
        <div class="p-info">
          <router-link to="/settings/personalInfo" class="p-info-ico">
            <img :src="headImg?headImg+headLimit:headImg|defaultAvatar" alt="">
          </router-link>
          <div class="p-info-text">
            <p><span v-text="valueName"></span></p>
            <p><span>远智编号:</span><span>{{yzCode}}</span></p>
            <p><span>手机号码:</span><span>{{mobile|hidePhone(6)}}</span></p>
          </div>
        </div>
        <div class="checkIn">
          <span :class="{checked:isChecked==true}" @click="checkIn">{{isChecked==true?"已签到":"签到"}}</span>
        </div>
      </div>
      <div class="settings-top-b">
        <router-link class="zhimi-info" to="/integral">
          <span v-text="~~accAmount||0"></span><span>&nbsp;智米 详情&gt;</span>
        </router-link>
        <div class="zhimi-pay">
          <!--<router-link :to="{name:'zhimiPay'}">充值</router-link>-->
        </div>
      </div>
    </div>
    <div class="settings-middle">
      <!--<div class="item">-->
        <!--<router-link :to="{name:'myCourse',query:{inviteId:authToken}}">-->
          <!--<div class="fl">-->
            <!--<i class="icon pic-course"></i><span>我的职业课程</span>-->
          <!--</div>-->
          <!--<div class="fr">-->
            <!--<i class="icon pic-right"></i>-->
          <!--</div>-->
        <!--</router-link>-->
      <!--</div>-->

      <!-- <div class="item">
        <router-link to="/settings/zhiiRecord">
          <div class="fl">
            <i class="icon pic-recording"></i><span>智米记录</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div> -->
      <!--<div class="item">-->
        <!--<router-link to="/settings/myEwm">-->
          <!--<div class="fl">-->
            <!--<i class="icon pic-erweima"></i><span>邀约二维码</span>-->
          <!--</div>-->
          <!--<div class="fr">-->
            <!--<i class="icon pic-right"></i>-->
          <!--</div>-->
        <!--</router-link>-->
      <!--</div>-->
      <div class="item">
        <router-link to="/settings/coupon">
          <div class="fl">
            <i class="icon pic-coupon"></i><span>奖学金</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item">
        <router-link to="/active/sprint">
          <div class="fl">
            <i class="icon pic-sprint"></i><span>我的邀请见证</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item">
        <router-link to="/myorder">
          <div class="fl">
            <i class="icon pic-order"></i><span>我的订单</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item mt">
        <router-link :to="{name:'invite',query:{inviteId:authToken}}">
          <div class="fl">
            <i class="icon pic-invited"></i><span>邀约</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item">
        <router-link to="/settings/enrollList">
          <div class="fl">
            <i class="icon pic-list"></i><span>助学排行榜</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item">
        <router-link to="/settings/myFans">
          <div class="fl">
            <i class="icon pic-myFans"></i><span>我的邀约</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item">
        <router-link :to="{name:'taskCard',query:{backUrl:'setting'}}">
          <div class="fl">
            <i class="icon pic-task"></i><span>任务卡</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
          <i class="task-status" v-if="taskNum!='0'">{{taskNum}}</i>
        </router-link>
      </div>
      <!-- <div class="item" v-if="isEmployee">
        <router-link to="/settings/myEnrollStu">
          <div class="fl">
            <i class="icon pic-myFans"></i><span>我的成考学员</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>       -->

      <div class="item mt">
        <router-link to="/settings/address">
          <div class="fl">
            <i class="icon pic-address"></i><span>地址管理</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div v-if="!isStudent" class="item">
        <router-link to="/settings/verificationMobile">
          <div class="fl">
            <i class="icon pic-phone"></i><span>更换手机号</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div v-if="isStudent" class="item">
        <router-link to="/settings/verificationIDCard">
          <div class="fl">
            <i class="icon pic-phone"></i><span>更换手机号</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
       <!--<div class="item" v-if="isEmployee">-->
        <!--<router-link :to="{name:'inviteLink',query: {scholarship:scholarship,scholarshipName:'newDreamBuild'}}">-->
          <!--<div class="fl">-->
            <!--<i class="icon pic-flower"></i><span>我的米瓣</span>-->
          <!--</div>-->
          <!--<div class="fr">-->
            <!--<i class="icon pic-right"></i>-->
          <!--</div>-->
        <!--</router-link>-->
      <!--</div>     -->
      <div class="item">
        <router-link :to="{name:'trainingCourse',query:{title:decodeURIComponent('读书会'),channelId:'220776'}}">
          <div class="fl">
            <i class="icon pic-reading"></i><span>读书会</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div  class="item">
        <router-link :to="{name:'newYearBoard'}">
          <div class="fl">
            <i class="icon pic-phone"></i><span>拜年海报生成工具</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item" v-if="!isEmployee">
        <!-- <a @click="toChangeMobile">
          <div class="fl">
            <i class="icon pic-phone"></i><span>手机号</span>
          </div>
          <div class="fr">
            <span style="vertical-align:middle;color:#999;">{{mobile}}</span><i class="icon pic-right"></i>
          </div>
        </a> -->
      </div>
      <!--<div class="item mt" v-else>-->
        <!--<router-link :to="{name:'trainingCourse'}">-->
          <!--<div class="fl">-->
            <!--<i class="icon pic-live"></i><span>内部培训</span>-->
          <!--</div>-->
          <!--<div class="fr">-->
            <!--<i class="icon pic-right"></i>-->
          <!--</div>-->
        <!--</router-link>-->
      <!--</div>-->
      <div class="item mt" v-if="isEmployee" @click="showTool" id="tool">
        <a href="javascript:;">
          <div class="fl">
            <i class="icon pic-tool"></i><span>助学工具</span>
          </div>
          <div class="fr">
            <i class="icon pic-down" v-if="!showFlag"></i>
            <i class="icon pic-top" v-else></i>
          </div>
        </a>
        <div class="items" v-if="showFlag">
          <router-link :to="{name:'inviteLink',query: {scholarship:scholarship,scholarshipName:'newDreamBuild'}}">
            <div class="fl">
              <i class="icon pic-flower"></i><span>我的米瓣</span>
            </div>
            <div class="fr">
              <i class="icon pic-right"></i>
            </div>
          </router-link>
          <router-link :to="{name:'trainingCourse'}">
            <div class="fl">
              <i class="icon pic-live"></i><span>内部培训</span>
            </div>
            <div class="fr">
              <i class="icon pic-right"></i>
            </div>
          </router-link>
          <a @click="toTool">
            <div class="fl">
              <i class="icon pic-card"></i><span>文章推荐工具</span>
            </div>
            <div class="fr">
              <i class="icon pic-right"></i>
            </div>
          </a>
        </div>
      </div>
      <router-view></router-view>
      <!--<div class="item mt">-->
        <!--<router-link :to="{name:'trainingCourse'}">-->
          <!--<div class="fl">-->
            <!--<i class="icon pic-live"></i><span>内部培训</span>-->
          <!--</div>-->
          <!--<div class="fr">-->
            <!--<i class="icon pic-right"></i>-->
          <!--</div>-->
        <!--</router-link>-->
      <!--</div>-->
      <!--<div class="item">
        <router-link to="/settings/signInfo">
          <div class="fl">
            <i class="icon pic-signInfo"></i><span>报考信息</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>
      <div class="item">
        <router-link to="/settings/idCard">
          <div class="fl">
            <i class="icon pic-idCard"></i><span>绑定身份证</span>
          </div>
          <div class="fr">
            <i class="icon pic-right"></i>
          </div>
        </router-link>
      </div>-->
    </div>
    <nav-bar></nav-bar>
    <share title="来远智,考学历" desc="已经超60w学员在这里学习，就差你了！" :link="shareLink" :imgUrl="linkImgUrl"  ref="share"/>
    <signshare v-if="signshare" @cancel="signshare=false" :isChecked="status"></signshare>
  </div>
</template>
<script>
  import navBar from '@/components/nav';
  import {isEmployee,isStudent,fmtDate} from '../../common';
  import signshare from './signshare'
  import share from '../../components/share.vue'
  import storage from '../../plugins/storage'
  import {activityTime} from "../../config"
  import axios from 'axios'
  import {setCookie, getCookie} from 'tiny-cookie';
  let scholarship ='47'; //优惠类型
  export default {
    data() {
      return {
        valueName: '',
        headImg: this.storage.getItem('headImg'),
        active: 'exchange',
        checked: '签到',
        yzCode: '',
        accAmount: 0,
        isChecked: false,
        isEmployee: isEmployee(),
        mobile: '',
        taskNum:'',
        signshare:false,
        inviteId:'',
        invite:{},
        shareLink:'',
        showInvite:false,
        scholarship:scholarship,
        learnId:'',
        linkImgUrl:'http://yzims.oss-cn-shenzhen.aliyuncs.com/icon/xlzs.jpg',
        isStudent:isStudent(),
        status:false,
        authToken:'',
        showFlag:false,
        headLimit:"?x-oss-process=image/resize,m_fixed,h_38,w_38",
      }
    },
    beforeRouteEnter(to,from,next){
      let phone = storage.getItem('mobile') || storage.getItem('phone') ;
      axios.post('/us/getUserIdCard/1.0/',{mobile:phone}).then(res=>{
        let {code,body} = res;
        if(body == null ) {
          storage.setItem('bindStudent','0');
        }else if(body.bindStudent== '1'){
          storage.setItem('bindStudent','1');
          storage.setItem('relation',body.relation);
        }else {
          storage.setItem('bindStudent','2');
          storage.setItem('relation',body.relation);
        }
        if(storage.getItem('bindStudent')=='0' || storage.getItem('bindStudent')=='2' ){
        next();
        }else {
          next({name:'roleAuth',query:{redirect:to.fullPath}})
        }
      })

    },
    created() {
      this.getUserTypes();
      this.inviteId = this.$route.query.inviteId || '';
      this.shareLink = `${window.location.origin}/settings?isSharePage=1`;
      this.learnId = this.storage.getItem('learnId') || '';
        let nowTime = new Date().getTime();
          let startTime = activityTime // 5月15日2点半测试切换
          if(nowTime > startTime) {
            scholarship = '71';
            this.scholarship = '71';
          }
    },
    mounted(){
      if(!!this.inviteId) {
        this.getInviteInfo();
      }
    },
    activated() {
      this.valueName = this.storage.getItem('zmcName');
      this.authToken = this.storage.getItem('authToken') || '';
      this.yzCode = this.storage.getItem('yzCode') || '无';
      this.mobile = this.storage.getItem('mobile') || '';
      this.headImg = this.storage.getItem('headImg');

      this.isSign();
      this.accountDetail();
      this.getTaskList();

      if (!this.mobile) {
        this.getUserInfo();
      }
    },
    methods: {
      showTool(){
        this.showFlag=!this.showFlag;
        setTimeout(()=>{
          if(this.showFlag){
            document.querySelector('html').scrollTop = document.getElementById("tool").offsetTop;
            document.getElementsByTagName('body')[0].scrollTop = document.getElementById("tool").offsetTop;
          }
        },100)
      },
      // 获取邀约人信息
       getInviteInfo() {
            let inviteId = (window.sessionStorage.getItem('inviteId') || decodeURIComponent(this.$route.query.inviteId || this.getQueryString(this.redirect, 'inviteId') || '')).replace(/ /g, '+');
            if(inviteId){
            this.$http.post('/us/getInviteInfo/1.0/', {inviteToken: inviteId}).then(res => {
                let {code, body} = res;
                if (code !== '00') return;
                this.invite = body||{};
                if(this.$route.query.isSharePage){
                  this.showInvite = true;
                }

            });
            }
        },
      toTool(){
        location.href=window.location.origin+'/teacherKit/home'
      },
      checkIn(){
        if (this.isChecked) {
          //this.$modal({message: '你今天已经签过到啦~'});
          this.status=true;
          this.signshare=true;
          return;
        }
        localStorage.removeItem('share');
        this.$indicator.open();
        this.$http.post('/us/sign/1.0/').then(res => {
          this.$indicator.close();
          if (res.code == '00') {
            let zm = res.body;
            const h = this.$createElement;
            this.isChecked = true;
            window.sessionStorage.setItem('isChecked', true)
            setCookie('isChecked', true, {expires: '30m'});
            localStorage.setItem("signTime",fmtDate(Date.now()).substr(0,10));
            this.accAmount += ~~zm;
             this.signshare = true;
          }
        }).catch(err => {
          this.$indicator.close();
        });
      },
      // 判断是否签到
      isSign() {
        let signTime=localStorage.getItem('signTime');
        let isC = JSON.parse(sessionStorage.getItem('isChecked'));
        if(fmtDate(Date.now()).substr(0,10)==signTime) {
          this.isChecked = true;
          return;
        }
        if(getCookie('isChecked')=='false'){
          this.isChecked=false;
          return;
        }
        this.isChecked = isC;
        this.$http.post('/us/isSign/1.0/').then(res => {
           const datas = res.body || false;
          setCookie('isChecked', datas, {expires: '30m'});
           if (datas != isC) {
              this.isChecked = datas;
              sessionStorage.setItem('isChecked', datas)
           } else {
              this.isChecked = isC;
           }
        });
      },
      // 账户详情
      accountDetail() {
        this.$http.post('/ats/accountDetail/1.0/', {accType: '2'}).then(res => {
          if (res.code !== '00') return;
          this.accAmount = ~~(res.body || {}).accAmount;
        });
      },
      // 获取用户身份
      getUserTypes: function () {
        if (!!this.storage.getItem('authToken') && !this.isEmployee) {
          this.$http.post('/us/userTypes/1.0/').then(res => {
            const {code, body} = res;
            if (code === '00') {
              this.storage.setItem('relation', body);
              this.isEmployee = isEmployee();
            }
          });
        }
      },
      // 获取用户信息
      getUserInfo: function () {
        this.$http.post('/us/userInfo/1.0/').then(res => {
          if (res.code === '00') {
            const mobile = res.body.mobile || '';
            this.mobile = mobile;
            this.storage.setItem('mobile', mobile);
          }
        });
      },
      toChangeMobile: function () {
        if (this.mobile) {
          this.$router.push({name: 'changeMobile'});
        } else {
          this.$router.push({name: 'login', query: {action: 'bindMobile', redirect: this.$route.fullPath}})
        }
      },
      getTaskList() {
        this.$http.post("/mkt/taskCardList/1.0/").then(res => {
          if (res.code !== '00') return;
          let list=[];
          list = res.body.filter(item=>{
            // userId为空，任务为未领取，taskStatus不为4 任务为未过期
            if(!item.userId&&item.taskStatus!='4'){
              return item
            }
          });
          this.taskNum=list.length
        })
      },
    },
    components: {navBar,signshare,share}
  }
</script>
<style lang="less" scoped>
  @import "../../assets/less/variable";

  .pic-right {
    background-image: url("../../assets/image/public_ico_open_right.png");
    background-size: 100%;
  }

  .pic-recording {
    background: url("../../assets/image/settings/icon.png") no-repeat 0 -.84rem;
    background-size: 0.21rem auto;
  }

  .pic-myFans {
    background: url("../../assets/image/settings/ic_fans.png") no-repeat ;
  }

  .pic-coupon {
    background: url("../../assets/image/settings/ic_coupon.png") no-repeat;
  }

  .pic-address {
    background: url("../../assets/image/settings/ic_address.png") no-repeat ;
  }
  .pic-list{
    background: url("../../assets/image/settings/ic_list.png") no-repeat ;
  }

  .pic-idCard {
    background-image: url("../../assets/image/settings/ico-idCard.png");
  }
  .pic-erweima {
    background-image: url("../../assets/image/settings/ic_invite.png");
  }
  .pic-signInfo {
    background-image: url("../../assets/image/settings/icon-signInfo.png");
  }
  .pic-live {
    background: url("../../assets/image/settings/ic_live.png") no-repeat;
  }
  .pic-reading {
    background: url("../../assets/image/settings/ic_reading.png") no-repeat;
  }
  .pic-task {
    background: url("../../assets/image/settings/ic_taskCard.png") no-repeat;
  }
  .pic-phone {
    background: url("../../assets/image/settings/ic_mobile.png") no-repeat;
  }
  .pic-flower {
    background: url("../../assets/image/settings/ic_miban.png") no-repeat;
  }
  .pic-invited {
    background: url("../../assets/image/settings/ic_invite.png") no-repeat ;
  }
  .pic-sprint{
    background: url("../../assets/image/settings/ic_sprint.png") no-repeat;
  }
  .pic-down{
    background-image: url("../../assets/image/public_ico_open_right.png");
    background-size: 100%;
    /*transform-origin: 50% 50%;*/
    /*transform: rotate(90deg);*/
  }
  .pic-top{
    background-image: url("../../assets/image/public_ico_open_right.png");
    background-size: 100%;
    transform-origin: 50% 50%;
    transform: rotate(90deg);
  }
  .pic-order{ background-image:url(../../assets/image/nav/ic_order.png); }
  .pic-tool{
    background: url("../../assets/image/settings/ic_tool.png") no-repeat ;
  }
  .pic-course{
    background: url("../../assets/image/settings/ic_course.png") no-repeat ;
  }
  .pic-card{
    background: url("../../assets/image/settings/ic_article.png") no-repeat;
  }
  .settings-top {
    background-image: url("../../assets/image/settings-bg.png");
    background-size: 100%;
    .settings-top-t {
      height:1.08rem; padding:.29rem .22rem;
      .p-info {
        float: left;
        .p-info-ico {
          float: left;
          width: 0.5rem;
          height: 0.5rem;
          margin-right: 0.2rem;
          border-radius: 50%;
          overflow: hidden;
          img {
            width: 100%;
            height: 100%;
          }
        }
        .p-info-text {
          float: left;
          p {
            margin: 0;
            color: #fff;
            line-height: 0.16rem;
            margin-top:.02rem;
            span {
              font-size: 0.14rem;
            }
            &:nth-of-type(2) {
              span {
                font-size: 0.12rem;
                font-weight: 500;
              }
            }
            &:nth-of-type(3) {
              span {
                font-size: 0.12rem;
                font-weight: 500;
              }
            }
          }
        }
      }
      .checkIn {
        float: right;
        line-height: 0.5rem;
        span {
          padding: 0.06rem 0.2rem;
          font-size: 0.12rem;
          color: #fff;
          font-weight: bold;
          border:0.5px solid #ffa3a3;
          border-radius: 0.2rem;
          // &.checked {
          //   padding:0.05rem 0.14rem;
          //   //background-color: #FF6960;
          //   border-color:#e03146;
          //   color:#cf3834;
          // }
        }
      }
    }
    .settings-top-b {
      height: 0.52rem;
      line-height: 0.52rem;
      background-color: rgba(235, 83, 78, 0.34);
      color: #fff;
      padding: 0 0.22rem;
      a {
        color: #fff;
        font-size: 0.12rem
      }
      .zhimi-info {
        float: left;
        line-height: 0.52rem;
        height: 0.52rem;
        span {
          font-size: 0.2rem
        }
        span:nth-of-type(2), a {
          font-size: 0.12rem;
          font-weight: 100
        }
        a, span {
          vertical-align: sub
        }
        a {
          line-height: 0.4rem;
          height: 0.4rem;
          display: inline-block;
        }
      }
      .zhimi-pay {
        float: right;
        line-height: 0.5rem;
        a {
          padding: 0.05rem 0.2rem;
          font-weight: bold;
          font-size: 0.12rem;
          color: #fff;
          border:1px solid #faba52;
          background-color:#faba52;
          border-radius: 0.2rem;
        }
      }
    }
  }

  .invite {
        background-image: url('../../assets/image/active/enrollmentHomepage/invitBg2.png');
        background-repeat: no-repeat;
        background-size: 100% 100%;
        height: .68rem;
        position: relative;
        margin-bottom: -.1rem;
        z-index: 99;
        img {
            width: .48rem;
            height: .48rem;
            float: left;
            border-radius: 50%;
            margin-top: 1.8%;
            margin-left: .12rem;
            margin-right: .07rem;
        }
        .rightView {
            float: left;
            margin-top: 2.3%;
            p {
                font-size: .16rem;
                span {
                    color: #e15443;
                    font-size: .13rem;
                }
                &:first-of-type {
                    font-size: .13rem;
                }
            }
        }
    }

  .settings-middle {
    .item {
      &.mt-10 {
        &:after {
          .borderBottom;
        }
      }
      a {
        position: relative;
        display: block;
        height: 0.56rem;
        line-height: 0.56rem;
        padding: 0 0 0 0.15rem;
        background-color: #fff;
        width: 100%;
        overflow: hidden;
        &:after {
          .borderBottom;
        }
        span {
          color: #444;
          font-size: 0.14rem;
          float: left;
        }
        i.icon {
          float: left;
          width: 0.32rem;
          height: 0.32rem;
          background-size: 100%;
          margin-right: 0.05rem;
          margin-top: .11rem;
          /*vertical-align: sub;*/
          &.pic-right,&.pic-down,&.pic-top {
            vertical-align: middle;
            width: 0.36rem;
            height: 0.36rem;
            margin-right: 0;
          }
        }
        i.task-status{
          position: absolute;
          display: inline-block;
          width: .16rem;
          height: .16rem;
          line-height: .16rem;
          vertical-align: middle;
          color: #fff;
          background-color: @color;
          border-radius: 50%;
          right: .35rem;
          top: .2rem;
          text-align: center;
          font-size: .1rem;
          padding: .01rem;
          font-style: normal;
        }
      }
    }
    .items{
      a{
        background-color: #f8f8f8;
      }
    }
  }
</style>
