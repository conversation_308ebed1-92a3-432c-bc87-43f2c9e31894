import config from "../config";
import storage from "../plugins/storage";
import bridge from "@/plugins/bridge";
import emotions from "../../static/emotion/emotions.json";
import { Toast } from "vant";
import { getCookie } from "tiny-cookie";

// 设置页面标题
export const setDocumentTitle = (title) => {
  document.title = title || config.title;
  if (
    document.title == "2019年成考全额奖学金" &&
    Date.now() > config.activityTime
  ) {
    document.title = "2021级筑梦计划";
  }
  if (isIOS()) {
    let iframe = document.createElement("iframe");
    iframe.style.display = "none";
    iframe.src = "/favicon.ico";
    iframe.onload = () => {
      setTimeout(() => {
        iframe.remove();
      }, 10);
    };
    document.body.appendChild(iframe);
  }
};

export const isVNode = (node) => {
  return (
    typeof node === "object" &&
    Object.prototype.hasOwnProperty.call(node, "componentOptions")
  );
};

// 路由导航守卫——加载省市区json文件
export const loadPcdJson = (to, from, next) => {
  if (!global.pcdJson) {
    const $script = document.createElement("script");
    $script.src = config.pcd;
    global.document.body.appendChild($script);

    $script.onload = () => {
      next();
    };
  } else {
    next();
  }
};

/**
 * 路由导航守卫
 * 1、ios系统在微信环境下分享功能需要刷新页面
 * 2、如果已登录，需要在url加上inviteId参数
 * */
export const inviteId = (to, from, next) => {
  let authToken = storage.getItem("authToken");
  const inviteId = decodeURIComponent(to.query.inviteId || "");

  // 如果url有inviteId并且不等于authToken，则缓存起来
  if (inviteId && inviteId !== authToken) {
    window.sessionStorage.setItem("inviteId", inviteId);
  }

  // 如果没有登录，authToken则从老缓存里取值
  if (!authToken) {
    authToken = storage.getItem("oldAuthToken");
  }
  if (
    (isIOS() && isWeixin() && from.matched.length > 0) ||
    (authToken && !inviteId)
  ) {
    let url = `${window.location.origin}${to.fullPath}`;
    if (authToken && !inviteId) {
      url = url
        .replace(/&?inviteId=[^&]*/g, "")
        .replace(/\?&/g, "?")
        .replace(/\?$/, "");
      url += `${url.includes("?") ? "&" : "?"}inviteId=${encodeURIComponent(
        authToken || ""
      )}`;
    }
    window.location.href = url;
  } else {
    next();
  }
};

/**
 * 格式化日期
 * formatDate(Date.now(),"yyyy-MM-dd hh:mm:ss.S") ==> 2006-07-02 08:09:04.423
 * formatDate(Date.now(),"yyyy-M-d h:m:s.S")      ==> 2006-7-2 8:9:4.18
 * */
export const fmtDate = (timestamp, format = "yyyy-MM-dd hh:mm:ss") => {
  if (!timestamp) return "";
  let date =
    "number" === typeof timestamp
      ? new Date(timestamp)
      : new Date(timestamp.replace(/-/g, "/"));
  if (date && !isNaN(date.getTime())) {
    date = currentZoneDate(date, 8); // 转换成东8区时间
  } else {
    return timestamp;
  }

  let fmt = format;
  let o = {
    "M+": date.getMonth() + 1, //月份
    "d+": date.getDate(), //日
    "h+": date.getHours(), //小时
    "m+": date.getMinutes(), //分
    "s+": date.getSeconds(), //秒
    "q+": Math.floor((date.getMonth() + 3) / 3), //季度
    S: date.getMilliseconds(), //毫秒
  };
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(
      RegExp.$1,
      (date.getFullYear() + "").substr(4 - RegExp.$1.length)
    );
  }
  for (let k in o) {
    if (new RegExp("(" + k + ")").test(fmt)) {
      fmt = fmt.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length)
      );
    }
  }
  return fmt;
};

/**
 * 转换成本地时间
 * */
function currentZoneDate(date, zoneOffset) {
  const offsetTime = new Date().getTimezoneOffset() * 60 * 1000; // 格林威治时间和本地时间之间的时差，以毫秒为单位
  return new Date(date.getTime() + offsetTime + zoneOffset * 60 * 60 * 1000);
}

/**
 * 转换日期格式，结果如20200308
 * @param time  时间格式，如new Date()时间戳
 * @param type  type为1则转换成yyyymmdd格式，type为2则转换成yyyymm格式, type为3时，转换为yyyy-mm-dd type为4时，转换为yyyy-mm
 * @returns {string}
 */
export const changeDate = (time, type) => {
  let temp_time = new Number(time);
  let temp_date = new Date(temp_time);
  let temp_year1 = "";
  let temp_month1 = "";
  let temp_day1 = "";
  if (type == 1) {
    temp_year1 = temp_date.getFullYear();
    temp_month1 =
      temp_date.getMonth() + 1 > 9
        ? temp_date.getMonth() + 1
        : "0" + (temp_date.getMonth() + 1);
    temp_day1 =
      temp_date.getDate() > 9 ? temp_date.getDate() : "0" + temp_date.getDate();
    return (
      temp_year1.toString() + temp_month1.toString() + temp_day1.toString()
    );
  } else if (type == 2) {
    temp_year1 = temp_date.getFullYear();
    temp_month1 =
      temp_date.getMonth() + 1 > 9
        ? temp_date.getMonth() + 1
        : "0" + (temp_date.getMonth() + 1);
    return temp_year1.toString() + temp_month1.toString();
  } else if (type == 3) {
    temp_year1 = temp_date.getFullYear();
    temp_month1 =
      temp_date.getMonth() + 1 > 9
        ? temp_date.getMonth() + 1
        : "0" + (temp_date.getMonth() + 1);
    temp_day1 =
      temp_date.getDate() > 9 ? temp_date.getDate() : "0" + temp_date.getDate();
    return (
      temp_year1.toString() +
      "-" +
      temp_month1.toString() +
      "-" +
      temp_day1.toString()
    );
  } else if (type == 4) {
    temp_year1 = temp_date.getFullYear();
    temp_month1 =
      temp_date.getMonth() + 1 > 9
        ? temp_date.getMonth() + 1
        : "0" + (temp_date.getMonth() + 1);
    temp_day1 =
      temp_date.getDate() > 9 ? temp_date.getDate() : "0" + temp_date.getDate();
    return temp_year1.toString() + "-" + temp_month1.toString();
  }
};

/**
 * 所有省份
 * */
export const provinceList = () => {
  let provinces = [];
  for (let item of pcdJson) {
    provinces.push(item.provinceName);
  }
  return provinces;
};

/**
 * 某省份的城市列表
 * */
export const cityList = (provinceCode) => {
  if (!provinceCode) return [];
  const list = pcdJson.find((val) => val.provinceCode === provinceCode) || {};
  return list.city || [];
};

/**
 * 某城市的区列表
 * */
export const districtList = (provinceCode, cityCode) => {
  if (!provinceCode || !cityCode) return [];
  const list =
    cityList(provinceCode).find((val) => val.cityCode === cityCode) || {};
  return list.district || [];
};

/**
 * 省名称
 * */
export const provinceName = (provinceCode) => {
  if (!provinceCode) return "";
  const list = pcdJson.find((val) => val.provinceCode === provinceCode) || {};
  return list.provinceName || "";
};

/**
 * 市名称
 * */
export const cityName = (cityCode, provinceCode) => {
  if (!provinceCode || !cityCode) return "";
  const list =
    cityList(provinceCode).find((val) => val.cityCode === cityCode) || {};
  return list.cityName || "";
};

/**
 * 区名称
 * */
export const districtName = (districtCode, provinceCode, cityCode) => {
  if (!provinceCode || !cityCode || !districtCode) return "";
  const list =
    districtList(provinceCode, cityCode).find(
      (val) => val.districtCode === districtCode
    ) || {};
  return list.districtName || "";
};

/**
 * 省code
 * */
export const provinceCode = (provinceName) => {
  if (!provinceName) return "";
  const list = pcdJson.find((val) => val.provinceName === provinceName) || {};
  return list.provinceCode || "";
};

/**
 * 市code
 * */
export const cityCode = (cityName, provinceCode) => {
  if (!provinceCode || !cityName) return "";
  const list =
    cityList(provinceCode).find((val) => val.cityName === cityName) || {};
  return list.cityCode || "";
};

/**
 * 区code
 * */
export const districtCode = (districtName, provinceCode, cityCode) => {
  if (!provinceCode || !cityCode || !districtName) return "";
  const list =
    districtList(provinceCode, cityCode).find(
      (val) => val.districtName === districtName
    ) || {};
  return list.districtCode || "";
};

/**
 * 是否已登陆
 */
export const isLogin = () => {
  const authToken = storage.getItem("authToken");
  return authToken && authToken != "";
};

/**
 * 是否是苹果系统
 * */
export const isIOS = function () {
  return /ip(hone|od|ad)/i.test(navigator.userAgent);
};

/**
 * 是否是安卓系统
 * */
export const isAndroid = function () {
  return (
    navigator.userAgent.indexOf("Android") > -1 ||
    navigator.userAgent.indexOf("Adr") > -1
  );
};

/**
 * 判断是否为微信浏览器
 * */
export const isWeixin = function () {
  return (
    navigator.userAgent.toLowerCase().match(/MicroMessenger/i) ==
    "micromessenger"
  );
};

/**
 * 判断是否为企业微信浏览器
 * */
export const isWxwork = function () {
  return (
    window.navigator.userAgent.toLowerCase().match(/MicroMessenger/i) ==
      "micromessenger" &&
    window.navigator.userAgent.toLowerCase().match(/wxwork/i) == "wxwork"
  );
};

/**
 *判断是否为QQ浏览器
 */
export const isQQ = function () {
  return navigator.userAgent.toLowerCase().match(/QQ/i) == "qq";
};
/**
判断是否为微博
 */
export const isWeibo = function () {
  return navigator.userAgent.toLowerCase().match(/WeiBo/i) == "weibo";
};
/**
 * 手机码验证
 * */
export const isphone = function (phone) {
  const reg = /^1[3|4|5|6|7|8|9][0-9]{9}$/;
  return reg.test(phone);
};

/**
 * 是否是iphonex尺寸
 * 是否是iphonexmax  xr 等
 */
export const isIphoneX = () => {
  if (!isIOS()) return false;
  var devicePixelRatio = window.devicePixelRatio; //设备像素比 ipx＝3 | 2
  var scrWidth = window.document.documentElement.getBoundingClientRect().width;
  var scrheight = window.screen.height;
  // iPhone XS Max /11promax
  var isIPhoneXSMax =
    devicePixelRatio === 3 && scrWidth === 414 && scrheight === 896;
  // iPhone XR
  var isIPhoneXR =
    devicePixelRatio === 2 && scrWidth === 414 && scrheight === 896;
  // iphone SE
  // var isIPhoneSE = devicePixelRatio === 2 && scrWidth === 375 && scrheight === 667;
  // iphone 11
  var isIPhone11 =
    devicePixelRatio === 2 && scrWidth === 414 && scrheight === 896;
  // iphone 11Pro/x/xs
  var isIPhoneXs =
    devicePixelRatio === 3 && scrWidth === 375 && scrheight === 812;
  // iphone 12 mini
  var isIPhone12Mini =
    devicePixelRatio === 3 && scrWidth === 360 && scrheight === 780;
  // iphone 12/12pro
  var isIPhone12 =
    devicePixelRatio === 3 && scrWidth === 390 && scrheight === 844;
  // iphone 12 ProMax
  var isIPhone12Pro =
    devicePixelRatio === 3 && scrWidth === 428 && scrheight === 926;

  return (
    isIPhoneXSMax ||
    isIPhone11 ||
    isIPhoneXs ||
    isIPhone12Mini ||
    isIPhone12 ||
    isIPhone12Pro
  );
};

/**
判断是否为pc电脑端
 */
export const isPc = function () {
  return !/Android|webOS|iPhone|iPod|BlackBerry/i.test(navigator.userAgent);
};

/**
 * 电话号码验证
 * */
export const isTel = function (tel) {
  const reg = /^([0\+]\d{2,3}-)?(\d{3,4}-)?\d{7,8}(-(\d{1,}))?$/;
  return reg.test(tel);
};

// 邮箱验证
export const isEmail = function (email) {
  const reg = /^[a-zA-Z0-9_-]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/;
  return reg.test(email);
};

// 身份证号码验证
export const isIDCard = function (isIDCard) {
  const reg =
    /(^[1-9]\d{5}(18|19|([23]\d))\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$)|(^[1-9]\d{5}\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{2}[0-9Xx]$)/;
  return reg.test(isIDCard);
};

// 香港身份证号码验证
export const isIDCardHk = function (isIDCard) {
  const reg = /[A-Z]{1,2}[0-9]{6}([0-9A])/;
  return reg.test(isIDCard);
};

// 澳门身份证号码验证
export const isIDCardMc = function (isIDCard) {
  const reg = /^[1|5|7][0-9]{6}\([0-9Aa]\)/;
  return reg.test(isIDCard);
};

// 台湾身份证号码验证
export const isIDCardTw = function (isIDCard) {
  const reg = /[A-Z][0-9]{9}/;
  return reg.test(isIDCard);
};

// 是否是学员
export const isStudent = function () {
  const relation = storage.getItem("relation");
  return !!(relation & config.N_USER_TYPE_STUDENT);
};

// 是否是员工
export const isEmployee = function () {
  const relation = storage.getItem("relation");
  return !!(relation & config.N_USER_TYPE_EMPLOYEE);
};
// 是否是粉丝
export const isFans = function () {
  const relation = storage.getItem("relation");
  return !!(relation == config.N_USER_TYPE_FANS);
};
// 过滤emoji表情
export const filterEmoji = function (val) {
  const reg =
    /[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF][\u200D|\uFE0F]|[\uD83C|\uD83D|\uD83E][\uDC00-\uDFFF]|[0-9|*|#]\uFE0F\u20E3|[0-9|#]\u20E3|[\u203C-\u3299]\uFE0F\u200D|[\u203C-\u3299]\uFE0F|[\u2122-\u2B55]|\u303D|[\A9|\AE]\u3030|\uA9|\uAE|\u3030/gi;
  return val.replace(reg, "");
};

// 跳转到登录页
export const toLogin = function (msg, action, redirect) {
  const jump = () => {
    this.$router.replace({
      name: "login",
      query: {
        redirect: redirect || this.$route.fullPath,
        inviteId: this.$route.query.inviteId || "",
        did: this.$route.query.inviteId || undefined,
        scholarship: this.$route.query.scholarship || "",
        action: action || "",
        plan: this.$route.query.plan,
        regOrigin:
          this.$route.query.regOrigin ||
          window.sessionStorage.getItem("regOrigin") ||
          "",
        regChannel: this.$route.query.regChannel || "",
        regExtParam: this.$route.query.mappingId || "",
        birthdayUserId: this.$route.query.brithdayUserId || "",
        shareName: this.$route.query.name,
      },
    });
  };

  if (msg === null) {
    jump();
  } else {
    this.$modal({
      message: msg || "请先登录",
      icon: "warning",
      beforeClose: (action, instance, done) => {
        done();
        jump();
      },
    });
  }
};

/**
 * 下载app
 */
export const downloadApp = () => {
  const u = navigator.userAgent;
  const isiOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); // ios终端

  if (isWxwork()) {
    Toast("请前往应用商店下载远智教育app");
    return;
  }
  if (!isiOS) {
    window.location.href =
      "https://sj.qq.com/myapp/detail.htm?apkName=cn.yzou.yzxt&info=584D5CE7C2E46DE462B7E60155D69798";
  } else {
    window.location.href = "https://apps.apple.com/cn/app/id1407258737";
  }
};

/**
 * 判断是否是当前年级
 * @param {String}} grade 年级
 */
export const isCurrentGrade = (grade) =>
  // --changeEnroll--
  grade == "2021" || grade == "2022" || grade == "2023" || grade == "2024" || grade == "2025" || grade == "2026";

/**
 * 判断当前时间是否在时间段里面
 * @param {Number} start 开始时间戳
 * @param {Number} end 结束时间戳
 */
export const isInTimeQuantum = (start, end) => {
  if (!start || !end) {
    console.error("请传入开始时间和结束时间");
    return false;
  }
  const now = Date.now();
  return now >= start && now <= end;
};

/**
 * 通过类型获取活动是否在时间段内 记得备注
 * @param {String} type 类型名称
 */
export const getIsInTimeByType = (type) => {
  if (type == "yy5m") {
    // 邀约6月活动  2020-6月15日18:00--2020-6月24日23:59:59
    const start = new Date(2020, 6, 21, 19, 0, 0).getTime(); // 月份从0开始
    const end = new Date(2020, 6, 31, 23, 59, 59).getTime();
    return isInTimeQuantum(start, end);
  }
  if (type == "decisive") {
    // 邀约6月活动  2020-6月15日18:00--2020-6月24日23:59:59
    const start = new Date(2020, 7, 9, 19, 0, 0).getTime(); // 月份从0开始
    const end = new Date(2020, 8, 10, 16, 0, 0).getTime();
    return isInTimeQuantum(start, end);
  }
  return "";
};

export const splitTime = (date) => {
  const starttoday = new Date(parseInt(date));
  const timedetail = {};
  timedetail.Year = starttoday.getFullYear();
  timedetail.Month = starttoday.getMonth() + 1;
  timedetail.Date = starttoday.getDate();
  timedetail.Hour = starttoday.getHours();
  if (starttoday.getMinutes() < 10) {
    timedetail.mit = starttoday.getMinutes() + "0";
  } else {
    timedetail.mit = starttoday.getMinutes();
  }
  return (
    timedetail.Year +
    "年" +
    timedetail.Month +
    "月" +
    timedetail.Date +
    "日" +
    timedetail.Hour +
    ":" +
    timedetail.mit
  );
};
/**
 * 是否是新的活动类型
 * @param {String} scholarship 活动类型
 */
export const isNewScholarship = (scholarship) => {
  return [
    "50",
    "71",
    "72",
    "73",
    "74",
    "78",
    "100",
    "103",
    "108",
    "121",
    "123",
    "125",
    "126",
    "127",
    "128",
    "129",
    "130",
    "141",
    "153",
    "154",
    "155",
    "156",
    "158",
    "159",
    "161",
    "164",
    "178",
    "179",
    "180",
    "152",
    "160",
    "181",
    "182",
    "183",
    "184",
    "200",
    "201",
    "202",
    "203",
    "204",
    "211",
    "212",
    "213",
    "214",
    "216",
    "217",
    "238",
    "243",
    "244",
    "241",
  ].includes(scholarship);
};

/**
 * 是否是奖学金挑战活动类型
 * @param {String} scholarship 活动类型
 */
export const isActScholarship = (scholarship) => {
  return [
    "180",
    "152",
    "160",
    "153",
    "154",
    "155",
    "156",
    "158",
    "159",
    "181",
    "161",
    "178",
    "179",
    "182",
    "183",
    "200",
    "201",
    "202",
    "203",
    "204",
    "211",
    "212",
    "213",
    "214",
    "216",
    "217",
    "238",
    "243",
    "244",
    "241",
  ].includes(scholarship);
};

/**
 * 根据奖学金挑战活动类型获取对应的活动页路由名
 * @param {String} scholarship 活动类型
 */
export const finActScholarshipUrl = (scholarship, unvsId) => {
  const actList = [
    { scholarship: "180", routeName: "signAct" },
    { scholarship: "152", routeName: "signAct2023" },
    { scholarship: "160", routeName: "signActTieLu2023" },
    { scholarship: "153", routeName: "signActCommerce" },
    { scholarship: "154", routeName: "signActEcology" },
    { scholarship: "155", routeName: "signActGuangShang" },
    { scholarship: "156", routeName: "signActHuaShang" },
    { scholarship: "158", routeName: "signActSongShan" },
    { scholarship: "159", routeName: "signActHuaShangNew" },
    { scholarship: "181", routeName: "signActTieLu" },
    { scholarship: "161", routeName: "signActHuaXia" },
    { scholarship: "178", routeName: "signActGuangRuan" },
    { scholarship: "179", routeName: "signActSongShan2023" },
    { scholarship: "182", routeName: "a20230829HuaShang" },
    { scholarship: "183", routeName: "a20230829DongGuan" },
    { scholarship: "200", routeName: "a20240417SongShan" },
    { scholarship: "201", routeName: "a20240417GuangShang" },
    { scholarship: "202", routeName: "a20240704ChengJian" },
    { scholarship: "203", routeName: "a20240819GuangRuan" },
    { scholarship: "204", routeName: "a20240819HuaShang" },
    { scholarship: "211", routeName: "a20241023DongGuan" },
    { scholarship: "212", routeName: "a20241023GuangTie" },
    { scholarship: "213", routeName: "a20241023GuangSheng" },
    { scholarship: "214", routeName: "2024ChengJian" },
    { scholarship: "216", routeName: "2024GuangJiu" },
    { scholarship: "217", routeName: "2024signActHuaXia" },
    {
      scholarship: "243",
      unvsInfo: [
        {
          routeName: 'a20250609ChengJian',
          unvsId: '153180146776432500'
        },
        {
          routeName: 'a20250609NanFang',
          unvsId: '171946300794145455'
        },
        {
          routeName: 'a20250609HuaXia',
          unvsId: '55'
        },
        {
          routeName: 'ScholarshipCampaign',
          params: {
            campaignKey: 'shangXueYuan2026'
          },
          unvsId: '52'
        },
        {
          routeName: 'ScholarshipCampaign',
          params: {
            campaignKey: 'dongGuan2026'
          },
          unvsId: '168128447920058546'
        },
        {
          routeName: 'ScholarshipCampaign',
          params: {
            campaignKey: 'xinHua2026'
          },
          unvsId: '168984905050474751'
        },
      ]
    },
    {
      scholarship: "244",
      unvsInfo: [
        {
          routeName: 'a20250609ChengJian',
          unvsId: '153180146776432500'
        },
        {
          routeName: 'a20250609NanFang',
          unvsId: '171946300794145455'
        },
        {
          routeName: 'a20250609HuaXia',
          unvsId: '55'
        },
        {
          routeName: 'ScholarshipCampaign',
          params: {
            campaignKey: 'shangXueYuan2026'
          },
          unvsId: '52'
        },
        {
          routeName: 'ScholarshipCampaign',
          params: {
            campaignKey: 'dongGuan2026'
          },
          unvsId: '168128447920058546'
        },
        {
          routeName: 'ScholarshipCampaign',
          params: {
            campaignKey: 'xinHua2026'
          },
          unvsId: '168984905050474751'
        },
      ]
    },
    { scholarship: "241", routeName: "a20250609NanFang" },
    { scholarship: "238", routeName: "a20250609ChengJian" },
  ];
  let currentAct = [];
  actList.forEach((item) => {
    if (item.scholarship == scholarship) {
      if (item.unvsInfo) {
        item.unvsInfo.forEach((unvsItem) => {
          if (unvsItem.unvsId == unvsId) {
            currentAct.push({
              scholarship: item.scholarship,
              ...unvsItem
            });
          }
        });
      } else {
        currentAct.push(item);
      }
    }
  });

  return currentAct[0] || { routeName: "" };
};

/**
 * 转换时间为天时分秒
 * @param {Number} time 时间(单位：秒)
 */
export const transformSecond = (secondTime) => {
  if (typeof secondTime !== "number") {
    return "";
  }
  let day = 0;
  let hour = 0;
  let min = 0;
  let second = secondTime;
  if (secondTime >= 60) {
    second = parseInt(secondTime % 60);
    min = parseInt(secondTime / 60);
    if (min > 60) {
      min = parseInt((secondTime / 60) % 60);
      hour = parseInt(secondTime / 3600);
      if (hour > 24) {
        hour = parseInt((secondTime / 3600) % 24);
        day = parseInt(secondTime / 3600 / 24);
      }
    }
  }
  return { day, hour, min, second };
};

/**
 * 获取什么服务器环境 89 192 正式 本地
 */
export const getWhichService = () => {
  if (
    process.env.NODE_ENV === "production" &&
    process.env.RUN_ENV === "production"
  ) {
    return "pro"; // 正式
  } else if (
    process.env.NODE_ENV === "production" &&
    process.env.RUN_ENV === "devtest"
  ) {
    return "pre"; // 预发布
  } else if (process.env.NODE_ENV === "development") {
    // 本地
    return "dev";
  }
  // 测试环境
  return "test";
};

export const goApp = () => {
  wx.config({
    debug: true, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印
    appId: config.appId, // 必填，公众号的唯一标识
    timestamp: "", // 必填，生成签名的时间戳
    nonceStr: "", // 必填，生成签名的随机串
    signature: "", // 必填，签名
    jsApiList: [], // 必填，需要使用的JS接口列表
    openTagList: [], // 可选，需要使用的开放标签列表，例如['wx-open-launch-app']
  });
};

// 是否要关闭自考保障计划-10.1版本 在2020-10-16 0点 关闭
export const isNewSelf = Date.now() >= new Date(2020, 9, 16).getTime();

// 获取url参数
export const getQueryString = (url, name) => {
  if (!url) {
    return "";
  }
  const index = url.indexOf("?");
  const search = url.substr(index);
  let reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
  if (search) {
    let r = search.substr(1).match(reg);
    if (r != null) return decodeURI(r[2]);
  }
  return null;
};
export const creatDeviceId = () => {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  }
  return (
    S4() +
    S4() +
    "-" +
    S4() +
    "-" +
    S4() +
    "-" +
    S4() +
    "-" +
    S4() +
    S4() +
    S4()
  );
};

/**
 * 转换图像到base64地址
 * @param {Object} img 图像，image对象
 */
export const transformImgToBase64 = (img, imgType = "image/png") => {
  const canvas = document.createElement("canvas");
  canvas.width = img.width;
  canvas.height = img.height;
  const ctx = canvas.getContext("2d");
  ctx.drawImage(img, 0, 0, img.width, img.height);
  const dataURL = canvas.toDataURL(imgType); // 可选其他值 image/jpeg
  return dataURL;
};

//判断用户联网类型
export const getNetworkType = () => {
  var ua = navigator.userAgent;
  var networkStr = ua.match(/NetType\/\w+/)
    ? ua.match(/NetType\/\w+/)[0]
    : "NetType/other";
  networkStr = networkStr.toLowerCase().replace("nettype/", "");
  var networkType;
  switch (networkStr) {
    case "wifi":
      networkType = "wifi";
      break;
    case "4g":
      networkType = "4g";
      break;
    case "3g":
      networkType = "3g";
      break;
    case "3gnet":
      networkType = "3g";
      break;
    case "2g":
      networkType = "2g";
      break;
    default:
      networkType = "other";
  }
  return networkType;
};
/**
 * 主要用于获取input光标的位置
 * @type {getTxtCursorPosition}
 */
export const getTxtCursorPosition = (elem) => {
  let input = elem;
  let cursorPosition = -1; //存放光标位置

  if (input.selectionStart) {
    //非IE浏览器
    cursorPosition = input.selectionStart;
  } else {
    //IE
    try {
      let range = document.selection.createRange();
      range.moveStart("character", -input.value.length);
      cursorPosition = range.text.length;
    } catch (error) {}
  }

  return cursorPosition;
};
/* 是否关闭 关闭公众号上课功能通告  2021年3月15日关闭。 过了15日就可以删掉这段代码
 */
export const isCloseNotice = () => {
  const closeDate = new Date(2021, 2, 15).getTime();
  return Date.now() >= closeDate;
};

//表情转换 [微笑] => <img src="xxx">
export const formatEmotions = (content) => {
  let emotionUrl = "//livestatic.polyv.net/assets/images/em/";
  if (!content) return content;
  return content.replace(/(\[([^\[^\]]*)\])/g, ($1) => {
    const cur = emotions.find((item) => {
      if ($1 === `[${item.title}]`) return item;
    });
    if (cur) {
      return `<img class="emotionimg" src="${emotionUrl + cur.fileName}"/>`;
    } else {
      return "";
    }
  });
};

// 匹配所有“\r\n”的正则表达式，把换行符替换成 br
export const formatWrap = (string) => {
  if (!string) return;
  return string.replace(/(\r\n)|(\n)/g, "<br>");
};

/**
 * 获取是否app打开
 * @param {Function} cb 回调函数
 */
export const getIsAppOpen = (cb) => {
  bridge
    .callHandler("isAppOpen")
    .then((res) => {
      cb && cb(res.appOpen);
    })
    .catch(() => {});
};

/**
 * 版本号对比
 * @param {string} curV 当前版本
 * @param {string} reqV 指定版本判断
 */
export const version = (curV, reqV) => {
  if (typeof curV === "string" && typeof reqV === "string") {
    let curVArr = curV.split(".");
    let reqVArr = reqV.split(".");

    let minL = Math.min(curVArr.length, reqVArr.length);
    let pos = 0; //当前比较位index
    let diff = 0; //当前为位比较是否相等

    //逐个比较如果当前位相等则继续比较下一位
    while (pos < minL) {
      diff = parseInt(curVArr[pos]) - parseInt(reqVArr[pos]);
      if (diff !== 0) {
        break;
      }
      pos++;
    }
    // 大于 0 当前APP版本为最新 or 等于 0 版本相同 返回true  小于0说明小于
    return diff > 0 || diff === 0;
  }

  return false;
};

/**
 * copy 文本
 * @param {string} text 要复制的文本内容
 **/
export const CopyElemText = (text) => {
  // 动态创建 textarea 标签
  const textarea = document.createElement("textarea");
  // 将该 textarea 设为 readonly 防止 iOS 下自动唤起键盘，同时将 textarea 移出可视区域
  textarea.readOnly = "readonly";
  textarea.style.position = "absolute";
  textarea.style.left = "-9999px";
  // 将要 copy 的值赋给 textarea 标签的 value 属性
  textarea.value = text;
  // 将 textarea 插入到 body 中
  document.body.appendChild(textarea);
  // 选中值并复制
  textarea.select();
  const result = document.execCommand("Copy");
  if (result) {
    Toast("复制成功，快去分享给好友吧😉");
  }
  document.body.removeChild(textarea);
};

export const verifyVersion = (versionNumber, cb) => {
  let curVersion = getCookie("app_version") || "";
  let res = version(curVersion, versionNumber);
  if (res) {
    cb && cb();
  } else {
    Toast("当前App版本过低暂不支持该功能，请您升级App后再次尝试");
  }
};

/**
 * 防抖函数
 * @param {*} func 执行防抖的函数
 * @param {*} time 间隔时间
 * @returns
 */
export function debounce(func, time) {
  let timer = null;
  return function () {
    const context = this;
    const args = arguments;
    clearTimeout(timer);
    timer = setTimeout(() => {
      func.apply(context, args);
      clearTimeout(timer);
    }, time);
  };
}
