<template>
  <div class="mainsBox">
    <div class="mainsBox-head">
      <div class="head-rule" @click="ruleBtn">活动规则</div>
      <div class="head-rule" @click="recordsBtn">邀约记录</div>
    </div>
    <div class="mainsBox-btn">
      <!-- 默认页面4400元现金-未解锁 -->
      <img class="btn-imgs" :src="fiveSrc" alt="" />
      <!-- 默认页面1000元现金-未解锁 -->
      <img class="btn-imgs" :src="oneSrc" alt="" />
      <div class="btn-text" v-if="enrollmentInvite == 'pay_success'">邀请<span>1位好友报读</span>，成功缴费即可获得奖学金</div>
      <div class="btn-text" v-else>报读奖学金院校，完成任务即可获得<span>丰厚奖学金</span></div>
      <!-- 立即报读 或 邀请好友 -->
      <div :class="{ 'btn-btns': true, 'btn-actives': fixedInviteBtn }">
        <img class="btn-img1" :src="enrolySrc" alt="" @click="nowEnrollment" />
        <img class="btn-img2" src="@/assets/image/618invite/btns-active.png" alt="" />
      </div>
    </div>
    <div class="mainsBox-flow"></div>
    <div class="mainsBox-school"></div>
    <div class="mainsBox-charge">
      <div class="title2"></div>
      <div class="table">
        <div class="table-b">
          <table>
            <thead>
              <tr class="title_calss">
                <th colspan="4">理工类（高起专）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="text_name title_text">招生专业</td>
                <td class="text_study title_text">学费</td>
                <td class="text_book title_text">书杂费</td>
                <td class="text_system title_text">学制</td>
              </tr>
              <tr>
                <td class="text_name">
                  <p>工程造价</p>
                  <p>机电一体化技术</p>
                  <p>建筑工程技术</p>
                  <p>计算机应用技术</p>
                </td>
                <td class="text_study">3600.00元/学年</td>
                <td class="text_book">400.00元/学年</td>
                <td class="text_system">2.5年</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="table">
        <div class="table-b">
          <table>
            <thead>
              <tr class="title_calss">
                <th colspan="4">外语类（高起专）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="text_name title_text">招生专业</td>
                <td class="text_study title_text">学费</td>
                <td class="text_book title_text">书杂费</td>
                <td class="text_system title_text">学制</td>
              </tr>
              <tr>
                <td class="text_name">
                  <p>商务英语</p>
                </td>
                <td class="text_study">3000.00元/学年</td>
                <td class="text_book">400.00元/学年</td>
                <td class="text_system">3年</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="table">
        <div class="table-b">
          <table>
            <thead>
              <tr class="title_calss">
                <th colspan="4">文史类（高起专）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="text_name title_text">招生专业</td>
                <td class="text_study title_text">学费</td>
                <td class="text_book title_text">书杂费</td>
                <td class="text_system title_text">学制</td>
              </tr>
              <tr>
                <td class="text_name">
                  <p>工商企业管理</p>
                  <p>大数据与会计</p>
                  <p>电子商务</p>
                </td>
                <td class="text_study">3600.00元/学年</td>
                <td class="text_book">400.00元/学年</td>
                <td class="text_system">2.5年</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
      <div class="table">
        <div class="table-b">
          <table>
            <thead>
              <tr class="title_calss">
                <th colspan="4">艺术类（高起专）</th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td class="text_name title_text">招生专业</td>
                <td class="text_study title_text">学费</td>
                <td class="text_book title_text">书杂费</td>
                <td class="text_system title_text">学制</td>
              </tr>
              <tr>
                <td class="text_name">
                  <p>室内艺术设计</p>
                </td>
                <td class="text_study">3600.00元/学年</td>
                <td class="text_book">400.00元/学年</td>
                <td class="text_system">2.5年</td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
    <div class="mainsBox-line">- 帮助每个人更上进 -</div>
    <!-- 微信右上角分享提示 -->
    <shareRightCornerWx @onClose="shareFlag = false" :shareFlag="shareFlag" :shareObs="shareRightObs" />
    <!-- 微信右上角内容分享组件 -->
    <share ref="schoolShare" v-if="shareObj.link" :title="shareObj.title" :desc="shareObj.desc" :link="shareObj.link" :imgUrl="shareObj.imgUrl" :regOrigin="shareObj.regOrigin" />
    <!-- 规则弹窗 -->
    <van-popup round v-model="ruleShow" @click-overlay="ruleShow = false" class="mainsBox-popup">
      <div class="popup-rules">
        <div class="rules-content">
          <p>活动背景</p>
          <p>为了鼓励更多的社会人士成为上进青年，不间断自己的学习步伐，经校办研究决定，推出“同桌共读奖学金”计划，详情如下：</p>
          <p>
            一、在2025年3月4日10:00:00——2025年4月3日23:59:59期间成功报读2026级广州城建职业学院的学员（以缴纳599元购买学习大礼包为准），打卡成功可获得4400元奖学金抵扣等值学费，同时邀请一名同行学员成功报读并缴费，可额外获得1000元奖学金抵扣等值学费。
          </p>
          <p>二、奖学金发放说明：</p>
          <p>（1）限购买599元上进礼包的学员参与。</p>
          <p>（2）奖学金激活：</p>
          <p>①在自报读之日起3个月内，完成累计21天的跑步打卡以及参与远智21天上进训练营后，方可激活4400元奖学金；</p>
          <p>
            ②在自报读之日起，在活动期间内，邀请好友注册并报读远智教育任一教育业务（成人高考、国家开放大学、广东开放大学、自学考试、研究生、职业教育等），好友接受邀请并完成报读缴费后，方可激活1000元奖学金。
          </p>
          <p>（3）奖学金以优惠券的形式发放，仅限抵减成教2026级广州城建职业学院第2年和第2.5/3年等值学费。</p>
          <p>【21天上进挑战奖学金挑战规则】</p>
          <p>完成两个挑战——“21天学习”和“21次跑步”，激活奖学金。</p>
          <p>「挑战一：21天学习」</p>
          <p>1、本挑战玩法总共包括21关卡，每天自动解锁1个关卡，需要学员在21天内完成。</p>
          <p>2、每位学员有5次补卡机会，遗漏的关卡可以在21天学习结束前进行补打卡，超过5次缺卡或者未在21天结束前完成所有补卡，将记作挑战失败。</p>
          <p>3、打卡要求，完成视频学习和学习心得发帖，记作有效打卡。</p>
          <p>「挑战二：21次跑步」</p>
          <p>1、本挑战需要累积21次跑步打卡，每天仅可进行1次跑步打卡，学员须在90天内完成21次打卡。</p>
          <p>2、在有效期内累计满21次有效打卡即算挑战成功，超过90天挑战期未完成21次打卡的，将记作挑战失败。</p>
          <p>3、单次打卡要求跑步距离不低于1公里（配速在10分/公里以内），并截图跑步数据上传打卡；不符合要求的、作弊或抄袭的，均记作无效打卡。</p>
          <p>注意事项：</p>
          <p>1、挑战一与挑战二可以同时进行，但是属于完全不同的两个独立挑战，需要学员分别去完成。</p>
          <p>2、挑战一失败、挑战二成功，则只需要重新完成挑战一即可。同理挑战二失败了，则只需要重新完成挑战二即可。</p>
          <p>3、只有在挑战一与挑战二均成功完成的情况下，才可以激活本次奖学金。</p>
          <p>4、每个挑战均有2次复训机会，若复训2次后还未挑战成功的同学，将失去挑战激活奖学金的资格。</p>
          <p>5、本次活动最终解释权归「远智教育」所有，通过非法或作弊行为获取本次挑战奖励，将取消奖学金获取资格，并将追回已发放奖励。</p>
          <p>【邀请好友报读奖学金规则】</p>
          <p>
            在2025年3月4日10:00:00——2025年4月3日23:59:59期间成功报读2026级广州城建职业学院的学员（以缴纳599元购买学习大礼包为准），邀请好友报读远智教育任一教育业务（成人高考、国家开放大学、广东开放大学、自学考试、研究生、职业教育等），好友接受邀请，并完成报读缴费，则视为邀请成功，可获得1000元奖学金，用于抵扣广州城建职业学院等值学费。
          </p>
          <p>注意事项：</p>
          <p>1、若好友在活动期间内发生退费，将取消奖学金获取资格，并同步扣回相应奖励；</p>
          <p>2、每位符合条件的城建学院在活动期间只能获得1次同桌共读奖学金。</p>
        </div>
      </div>
      <img class="popup-close" @click="ruleShow = false" src="@/assets/image/618invite/close.png" alt="" />
    </van-popup>
    <!-- 邀约弹窗 -->
    <van-popup round v-model="recordShow" @click-overlay="recordShow = false" class="mainsBox-popup">
      <div class="popup-record">
        <!-- 解锁4400元 或 5400元优惠券（有邀约数据并且邀约成功才会出现） -->
        <img class="record-tile" v-if="showRecordTile" :src="alreadySrc" alt="" />
        <!-- 邀约列表 -->
        <div class="record-content" :style="{ marginTop: !showRecordTile ? '0.8rem' : '0.24rem' }">
          <!-- 有邀约数据 -->
          <ul v-if="invitesList.length" class="record-islist">
            <li class="record-tabs"><span>姓名</span><span>邀请状态</span></li>
            <li class="record-li" v-for="(item, index) in invitesList" :key="index">
              <div class="record-box">
                <img class="record-head" :src="item.headImg" alt="" />
                <span class="record-span"> {{ item.userName }}</span>
              </div>
              <div class="record-box">
                <img class="record-icon" v-if="!item.status" src="@/assets/image/618invite/record-success.png" alt="" />
                <span> {{ item.status ? '待缴费' : '邀约成功' }}</span>
              </div>
            </li>
          </ul>
          <!-- 没有邀约好友 -->
          <div v-else class="record-nodata">
            <img class="record-imgs2" src="@/assets/image/618invite/record-no.png" alt="" />
            <p>还未解锁奖学金哦</p>
            <p>快去邀请好友报读吧~</p>
          </div>
        </div>
        <!-- 有邀约数据并且邀约成功时，隐藏按钮 -->
        <img class="record-btns" v-if="!showRecordTile && !invitesList.length" @click="invitesBtn(0)" src="@/assets/image/618invite/inviteBtn.png" alt="" />
      </div>
      <img class="popup-close" @click="recordShow = false" src="@/assets/image/618invite/close.png" alt="" />
    </van-popup>
  </div>
</template>

<script>
import { toAppLogin } from '@/common/jump'
import { formatTimeByMoment } from '@/filters'
import { isIphoneX, isLogin, toLogin, getIsAppOpen } from '@/common'
import shareRightCornerWx from '@/components/shareRightCornerWx'
import share from '@/components/share'
import { setRawCookie, getRawCookie } from 'tiny-cookie'

export default {
  components: { shareRightCornerWx, share },
  data() {
    return {
      userId: '',
      fiveSrc: require('@/assets/image/618invite/five.png'),
      oneSrc: require('@/assets/image/618invite/one.png'),
      enrolySrc: require('@/assets/image/618invite/enrollment.png'),
      alreadySrc: require('@/assets/image/618invite/record-already5.png'),
      // 默认是立即报读（邀约好友）
      enrollmentInvite: 'wait_apply',
      // 默认固定按钮位置
      fixedInviteBtn: false,
      // 弹窗
      ruleShow: false,
      recordShow: false,
      // 是否显示弹窗标题
      showRecordTile: false,
      // 邀约数据
      invitesList: [],
      // 判断是否登录
      isIphoneX: isIphoneX(),
      isLogin: isLogin(),
      isAppOpen: false,
      // 分享文案
      shareObj: {
        title: '来自好友的上进邀请',
        desc: 'Hi，我正在参加远智教育“同桌共读奖学金活动”，希望你能和我成为同桌~',
        link: '',
        imgUrl: 'https://yzpres.oss-cn-guangzhou.aliyuncs.com/yzstart.png',
        regOrigin: '118'
      },
      shareRightObs: {
        one: '奖学金邀约链接已生成',
        two: '请点击右上角',
        three: '将它发送给好友'
      },
      shareFlag: false
    }
  },
  created() {
    // 页面总曝光
    this.$sensors.track('show', {
      event_title: '活动页面总曝光',
      event_path: '/settings/sendStamps/UrbanConstructionInvite',
      event_time: formatTimeByMoment(new Date())
    })
    // 链接分享-1 海报分享-2
    const linkType = Number(this.$route.query?.linkType || 0)
    if (linkType) {
      this.$sensors.track('show', {
        event_title: `活动页面-老师分享${linkType == 2 ? '海报二维码' : linkType == 1 ? '链接' : ''}页面-曝光`,
        event_path: '/settings/sendStamps/UrbanConstructionInvite',
        event_time: formatTimeByMoment(new Date())
      })
    }
    console.clear()
    // 从cookie中获取，如果为2，则刷新页面
    if (getRawCookie('onceRefresh') == 2) {
      setRawCookie('onceRefresh', 0)
      this.$router.go(0)
    }
    // 必须先注册-登录
    this.userId = this.storage.getItem('userId')
    if (this.userId) this.getAll()
    else this.getUserID()
  },
  mounted() {
    // 滚动事件触发后执行的代码
    const that = this
    window.addEventListener('scroll', () => {
      that.fixedInviteBtn = Boolean(window.scrollY >= 650)
    })
    // 取值
    getIsAppOpen((bool) => {
      this.isAppOpen = bool
    })
  },
  methods: {
    // 获取用户userid
    getUserID() {
      const codes = this.storage.getItem('yzCode')
      console.log('用户yzCode', codes)
      this.$http.post('/mkt/getUserIdByYzCode/1.0/', { yzCode: codes }).then((res) => {
        if (res?.body) {
          this.userId = res?.body || ''
          this.storage.setItem('userId', res.body)
          this.getAll()
        }
      })
    },
    // 中间层
    getAll() {
      console.log('中间层-取用户-userid', this.userId)
      const userName = this.storage.getItem('realName')
      // 会自动加上登录者Token，作为邀约参数
      this.$set(this.shareObj, 'title', `来自${userName || '好友0000'}的上进邀请`)
      this.$set(this.shareObj, 'link', `${window.location.origin}/settings/sendStamps/UrbanConstructionEnrollment?inviteUserId=${encodeURIComponent(this.userId)}`)
      this.$nextTick(() => {
        this.$refs.schoolShare?.openTwo()
        this.getMyActiveInfo()
      })
    },
    // 规则
    ruleBtn() {
      this.$sensors.track('click', {
        event_title: '活动页【活动规则】点击',
        event_path: '/settings/sendStamps/UrbanConstructionInvite',
        event_time: formatTimeByMoment(new Date())
      })
      console.clear()
      this.ruleShow = true
    },
    // 邀约记录按钮
    recordsBtn() {
      this.$sensors.track('click', {
        event_title: '活动页【邀约记录】点击',
        event_path: '/settings/sendStamps/UrbanConstructionInvite',
        event_time: formatTimeByMoment(new Date())
      })
      console.clear()
      if (this.judgeLogin()) this.getMyInviteList()
    },
    // 立即报读或邀请按钮
    nowEnrollment() {
      /**
       *  payUnlockStatus: 缴费解锁状态
       *
       * 默认值  wait_apply   待报读（邀请人待报读）
       *         wait_pay     待缴费（邀请人已报读未缴费）
       *         pay_success  缴费成功（邀请人已缴费）
       */
      if (this.judgeLogin()) {
        // 前往报读页面：未报读状态
        if (this.enrollmentInvite == 'wait_apply') {
          this.$sensors.track('click', {
            event_title: '活动页【立即报读】点击',
            event_path: '/settings/sendStamps/UrbanConstructionInvite',
            event_time: formatTimeByMoment(new Date())
          })
          this.$router.push({
            path: '/invite/enroll?activityName=scholarship&scholarship=238&actName=26级城建同桌共读计划'
          })
        }
        // 前往缴费页面：已经报读了
        else if (this.enrollmentInvite == 'wait_pay') {
          this.$router.push({ path: '/student' })
        }
        // 右上角邀请好友：支付成功才有邀约
        else if (this.enrollmentInvite == 'pay_success') {
          this.invitesBtn(1)
        }
      }
    },
    // 判断是否已经登录
    judgeLogin() {
      if (!this.isLogin) {
        setRawCookie('onceRefresh', 1)
        if (!this.isAppOpen) {
          toLogin.call(this, null)
          return false
        }
        toAppLogin()
        return false
      }
      return true
    },
    // 弹窗里面的邀请好友
    invitesBtn(types) {
      this.$sensors.track('click', {
        event_title: `活动页${types ? '' : '-邀约记录-'}【邀请好友报读】点击`,
        event_path: '/settings/sendStamps/UrbanConstructionInvite',
        event_time: formatTimeByMoment(new Date())
      })
      console.clear()
      this.shareFlag = true
      this.recordShow = false
    },
    // 获取我的618邀请活动信息
    getMyActiveInfo() {
      this.$http
        .post('/mkt/getInvitationActivity/1.0/', {
          activityId: '238',
          invitationUserId: this.userId
        })
        .then((res) => {
          if (res?.code === '00' && res?.body) {
            const { payUnlockStatus, invitationUnlockStatus } = res?.body
            // 解锁立即报读
            if (payUnlockStatus) {
              this.enrollmentInvite = payUnlockStatus
              // 缴费成功-才变成邀约好友
              if (payUnlockStatus == 'pay_success') {
                this.fiveSrc = require('@/assets/image/618invite/fiveed.png')
                this.enrolySrc = require('@/assets/image/618invite/inviteBtn.png')
                // 解锁邀约记录弹窗-4400
                this.showRecordTile = true
                this.alreadySrc = require('@/assets/image/618invite/record-already5.png')
              }
            }
            // 解锁邀请好友
            if (invitationUnlockStatus) {
              this.oneSrc = require('@/assets/image/618invite/oneed.png')
              // 解锁邀约记录弹窗-1000
              this.showRecordTile = true
              this.alreadySrc = require('@/assets/image/618invite/record-already1.png')
              // 解锁邀约记录弹窗-5400
              if (payUnlockStatus == 'pay_success') {
                this.alreadySrc = require('@/assets/image/618invite/record-already6.png')
              }
            }
          }
        })
    },
    // 获取618邀请列表
    getMyInviteList() {
      this.$http
        .post('/mkt/getInvitationList/1.0/', {
          activityId: '238',
          pageNum: 1,
          pageSize: 100,
          invitationUserId: this.userId
        })
        .then((res) => {
          const { code, body } = res
          if (code == '00') {
            this.invitesList = body?.map((item) => {
              item.status = Boolean(item.invitationStatus == 'wait_pay')
              return item
            })
          }
          this.recordShow = true
        })
        .catch(() => {
          this.recordShow = true
          this.invitesList = []
        })
    }
  },
  beforeDestroy() {
    window.removeEventListener('scroll', window, false)
  }
}
</script>

<style lang="less" scoped>
.mainsBox {
  width: 100%;
  padding-bottom: 1rem;
  background-color: #f63959;

  .mainsBox-head {
    width: 100%;
    height: 3rem;
    background-image: url(@/assets/image/618invite/titles.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;

    .head-rule {
      width: 0.42rem;
      padding: 0.08rem 0.06rem;
      color: #ffffff;
      text-align: center;
      background: rgba(0, 0, 0, 0.7);
      border-radius: 0.15rem 0 0 0.15rem;
      position: absolute;
      top: 0.87rem;
      right: 0;
    }

    .head-rule:last-child {
      top: 1.61rem;
    }
  }

  .mainsBox-btn {
    width: 3.55rem;
    height: 3.53rem;
    margin: auto;
    text-align: center;
    background: #fefdf6;
    border-radius: 0.16rem;
    border: 0.01rem solid #ffffff;

    .btn-imgs {
      width: 3.35rem;
      height: 1rem;
      margin: 0.1rem 0;
    }

    .btn-text {
      width: 100%;
      margin-top: 0.08rem;
      font-weight: 400;
      font-size: 0.15rem;
      color: #2a0100;
      line-height: 0.23rem;

      span {
        font-weight: 500;
        color: #fd214f;
      }
    }

    .btn-btns {
      margin-top: 0.19rem;
      animation: centerAmplify 1s infinite;
      position: relative;

      .btn-img1 {
        width: 2.4rem;
        height: 0.48rem;
      }

      .btn-img2 {
        width: 0.58rem;
        height: 0.58rem;
        position: absolute;
        top: 0.15rem;
        right: 0.4rem;
      }
    }

    .btn-actives {
      position: fixed;
      left: 0;
      right: 0;
      bottom: 0.4rem;
      z-index: 20;
    }
  }

  .mainsBox-flow {
    width: 3.55rem;
    height: 6.19rem;
    margin: 0.16rem auto;
    background-image: url(@/assets/image/618invite/flows.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .mainsBox-school {
    width: 3.55rem;
    height: 2.04rem;
    margin: auto;
    background-image: url(@/assets/image/618invite/school.png);
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .mainsBox-charge {
    width: 3.55rem;
    margin: 0.1rem auto 0;
    padding-bottom: 0.1rem;
    border-radius: 0.16rem;
    background: #fefdf6;
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;

    .title2 {
      width: 3.55rem;
      height: 0.61rem;
      background-image: url(@/assets/image/618invite/school-name.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;
    }

    .table {
      width: 100%;
      overflow: hidden;
      margin: 0.1rem 0.04rem;
      border-radius: 0.1rem;
      background-color: #fff;
      border: 1px solid rgba(239, 186, 6, 1);
      position: relative;

      .table-t {
        height: 0.82rem;
        padding-top: 0.1rem;
        padding-left: 0.12rem;
        background: rgba(255, 244, 219, 1);
        border-radius: 10px 10px 0px 0px;

        .schoolName {
          height: 0.22rem;
          font-size: 0.16rem;
          font-family: PingFang-SC-Bold, PingFang-SC;
          font-weight: bold;
          color: rgba(193, 57, 53, 1);
          line-height: 0.22rem;
        }

        .schoolPf {
          color: rgba(54, 54, 54, 0.8);
          font-size: 13px;
        }

        .fr {
          width: 1.87rem;
          height: 0.17rem;
          font-size: 0.12rem;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: rgba(193, 57, 53, 1);
          line-height: 0.17rem;
        }

        .fl,
        .fr {
          float: none;
        }
      }

      .table-b {
        table {
          width: 100%;
          border-top: 1px solid rgba(239, 186, 6, 1);
          border-top: none;
          text-align: center;

          th {
            height: 0.2rem;
            font-size: 0.14rem;
            font-family: PingFang-SC-Bold, PingFang-SC;
            font-weight: bold;
            color: rgba(69, 56, 56, 1);
            line-height: 0.2rem;
          }

          td {
            width: 0.67rem;
            font-size: 0.12rem;
            font-weight: 600;
            vertical-align: middle;
            border-bottom: 1px solid rgba(239, 186, 6, 1);
            border-right: 1px solid rgba(239, 186, 6, 1);
            background-color: #fff;
            padding: 5px;
            font-family: PingFangSC-Regular, PingFang SC;
            color: rgba(69, 56, 56, 1);
            line-height: 0.17rem;

            &:last-of-type {
              border-right: none;
            }

            span {
              color: rgba(193, 57, 53, 1);
              font-weight: bold;
              font-size: 0.14rem;
            }
          }

          tr:first-child td {
            border-top: 1px solid rgba(239, 186, 6, 1);
          }

          tr:nth-last-of-type(1) td {
            border-bottom: none;
          }

          .text_top {
            height: 1.08rem;
          }

          .ric {
            color: #c13935;
          }

          .orage {
            color: #ed5206;
          }

          .text_name {
            width: 33.333333%;
            font-weight: 400;

            p {
              padding: 2px;
            }
          }

          .text_study {
            font-weight: 400;
          }

          .text_book {
            font-weight: 400;
            padding: 4px;
          }

          .text_system {
            width: 16.666667%;
            font-weight: 400;
          }

          .title_text {
            font-weight: 700;
            color: #444;
            font-size: 0.12rem;
            padding: 8px 0;
          }
        }

        .title_calss {
          height: 0.52rem;

          th {
            line-height: 0.52rem;
            border-radius: 0.1rem 0.1rem 0rem 0rem;
            background: #fffbf6;
            font-size: 0.18rem;
            color: #444;
            font-weight: 400;
          }
        }
      }

      &:after {
        content: '';
        position: absolute;
        width: 100%;
        height: 100%;
        background-color: #ffd100;
        right: -0.07rem;
        top: -0.1rem;
        z-index: -1;
      }
    }

    img {
      width: 0.8rem;
      height: 0.8rem;
      margin-bottom: 0.07rem;
    }
  }

  .mainsBox-line {
    width: 1.19rem;
    height: 0.17rem;
    margin: 0.3rem auto 0;
    font-weight: 400;
    font-size: 0.12rem;
    color: #ffffff;
    line-height: 0.17rem;
    text-align: center;
    font-style: normal;
  }

  .mainsBox-popup {
    height: 100vh;
    overflow: hidden;
    overflow-y: scroll;
    text-align: center;
    background-color: rgba(0, 0, 0, 0);

    .popup-rules {
      width: 3.15rem;
      height: 4.8rem;
      margin-top: 0.6rem;
      overflow: hidden;
      background-image: url(@/assets/image/618invite/rules.png);
      background-repeat: no-repeat;
      background-size: 100% 100%;

      .rules-content {
        width: 2.75rem;
        height: 3.98rem;
        margin: 0.51rem auto 0.31rem;
        overflow: hidden;
        overflow-y: scroll;
        text-align: left;
        font-weight: 400;
        font-size: 0.14rem;
        color: #2a0100;
        line-height: 0.24rem;
      }
    }

    .popup-record {
      padding-top: 0.1rem;

      .record-tile {
        width: 3.31rem;
        height: 0.99rem;
      }

      .record-content {
        width: 3.45rem;
        height: 3.91rem;
        overflow: hidden;
        background-image: url(@/assets/image/618invite/record-list.png);
        background-repeat: no-repeat;
        background-size: 100% 100%;

        .record-islist {
          width: calc(100% - 10.4rem);
          height: 80%;
          overflow: hidden;
          overflow-y: scroll;
          margin: 0.44rem auto 0;

          .record-tabs {
            width: 100%;
            height: 0.22rem;
            display: flex;
            font-weight: 500;
            font-size: 0.16rem;
            color: #cc2725;
            line-height: 0.22rem;
            font-style: normal;

            span {
              display: block;
              width: 50%;
              text-align: center;
            }

            span:last-child {
              text-align: right;
              margin-right: 0.2rem;
            }
          }

          .record-li {
            margin: 0.18rem 0.2rem 0;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .record-box {
              display: flex;
              align-items: center;

              .record-head {
                width: 0.32rem;
                height: 0.32rem;
                border-radius: 50%;
              }

              .record-icon {
                width: 0.18rem;
                height: 0.2rem;
              }

              span {
                margin-left: 0.08rem;
                font-weight: 400;
                font-size: 0.15rem;
                color: #2a0100;
                line-height: 0.21rem;
                text-align: left;
                font-style: normal;
              }

              .record-span {
                width: 0.9rem;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
              }
            }
          }
        }

        .record-nodata {
          margin: 0.79rem auto 0;

          .record-imgs2 {
            width: 1.58rem;
            height: 1.58rem;
            margin-bottom: 0.32rem;
          }

          p {
            font-weight: 400;
            font-size: 0.16rem;
            color: #2a0100;
            line-height: 0.28rem;
            font-style: normal;
          }
        }
      }

      .record-btns {
        width: 2.4rem;
        height: 0.48rem;
        margin: 0.27rem auto 0;
      }
    }

    .popup-close {
      width: 0.32rem;
      height: 0.32rem;
      margin-top: 0.3rem;
    }
  }

  // 隐藏滚动条
  ::-webkit-scrollbar {
    display: none;
  }

  // 动画 - 中心放大
  @keyframes centerAmplify {
    0% {
      transform: scale(0.8);
    }

    20% {
      transform: scale(0.9);
    }

    40% {
      transform: scale(1);
    }

    55% {
      transform: scale(1.1);
    }

    60% {
      transform: scale(1);
    }

    80% {
      transform: scale(0.9);
    }

    100% {
      transform: scale(0.8);
    }
  }
}
</style>
