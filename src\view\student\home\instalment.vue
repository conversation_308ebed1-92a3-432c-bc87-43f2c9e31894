<template>
  <div class="main-wrap">
    <yz-steps class="mb10" :stepNames="isShowStep" :current="topIndex" />
      <div v-if="stgInfo.status === 1" class="shadow-block p15">
        <h2 class="block-title mb10">订单信息</h2>
        <div class="cell mb10">
          <span class="sub-cell">课程总额</span>
          <span class="sub-cell text-right">
            {{stgInfo.orderAllAmount || 0}}元
          </span>
        </div>
        <div class="cell mb10">
          <span class="sub-cell">首付</span>
          <span class="sub-cell text-right">{{ stgInfo.downPaymentAmount || 0}}元</span>
        </div>
        <div class="cell mb10">
          <span class="sub-cell">优惠/抵扣</span>
          <span class="sub-cell text-right">
            {{stgInfo.deductionAmount || 0}}元
          </span>
        </div>
        <div class="cell mb10">
          <span class="sub-cell">贷款总额</span>
          <span class="sub-cell text-right">
            {{ stgInfo.installmentAmount || 0}}元
          </span>
        </div>
      </div>
      <div v-if="stgInfo.status === 1 || stgInfo.status === 2" class="loan-cycle-box">
        <h3 class="title">选择贷款期数</h3>
        <template>
          <div class="loan-time">
            <div v-for="item in stgInfo.installmentConfig.details" :key="item.id" :class="[{select: stagingNum === item.period},'loan-time-box']" @click="selectCycle(item)">{{item.period}}个月</div>
          </div>
          <div class="interest-rate-box">
            <div class="flex1">
              <p class="test" :class="{'rate-between': stagingNum}"><span>贷款利率</span><span v-if="stagingNum">{{ stagingInterest }}%</span></p>
              <span v-if="!stagingNum" class="placeholder">请选择期数</span>
            </div>
            <p v-if="subsidyProportion" :class="{ 'rate-between': subsidyProportion }">
              <span>贴息比例</span>
              <span>{{ parseFloat(subsidyProportion) }}%</span>
            </p>
          </div>
        </template>

        <div class="interest-plan flex1">
          <p>还款计划</p>
          <div class="interest-plan-r flex1" @click="asShow=true">
            <div>
              <p>总利息 ¥{{ totalInterest }}<span v-if="subsidyTotal">(已贴息 ¥ {{ subsidyTotal }})</span></p>
              <p>点击查看每月还款计划</p>
            </div>
            <img src="../../../assets/image/student/right-icon.png" alt="">
          </div>
        </div>
        <div class="tips">
          贷款利率及还款利息最终请以{{stageDict.name}}贷款页面为准，本页面提供的贷款信息仅供参考预览
        </div>
      </div>
      <div v-if="stgInfo.status === 2" class="user-info">
        <div class="user-info-head flex1">
          <h3 class="title">核对个人信息</h3>
          <div class="flex1 mb10" @click="editTel" v-if="installmentType == 32">
            <span>修改</span>
            <img src="../../../assets/image/student/edit-icon.png" alt="">
          </div>
        </div>
        <div class="user-info-connect">
          <div class="info-item">
            <span>姓 名：</span>
            <span>{{stgInfo.applyName}}</span>
          </div>
          <div class="info-item" v-if="installmentType == 32">
            <span>手机号码：</span>
            <span>{{mobile}}</span>
          </div>
          <div class="info-item">
            <span>身份证号：</span>
            <span>{{stgInfo.idCard}}</span>
          </div>
          <div class="tips">{{ stageDict.infoTip }}</div>
        </div>
      </div>
      <div v-if="stgInfo.status === 1" class="shadow-block question p15 mb10">
        <h2 class="block-title">贷款常见问题</h2>
        <div class="item">
          <p class="topic">是在哪个平台上贷的款？</p>
          <p class="answer">贷款服务全程由{{ stageDict.name }}提供</p>
        </div>
        <div class="item">
          <p class="topic">借款有哪些申请条件？</p>
          <p class="answer">{{ stageDict.age }}周岁-55周岁之间的中国大陆居民、征信良好，有银行借记卡。</p>
        </div>
        <div class="item">
          <p class="topic">贷款会上报征信吗？</p>
          <p class="answer">根据中国人民银行规定，贷款会上报征信系统。</p>
        </div>
        <div class="item">
          <p class="topic">提前还款需要违约金吗？</p>
          <p class="answer" style="color: #f17674;">{{ stageDict.earlyPay }}</p>
        </div>
        <div class="item">
          <p class="topic">借款失败怎么办？</p>
          <p class="answer">申请额度或者操作借款审核失败/暂不能提供服务是因为综合评估未通过，综合评估和经济环境、政策调整，以及您的信用、
            职业收入、借还款情况等相关，目前审核结果人工也无法干预哦</p>
        </div>
      </div>
    <van-action-sheet v-model="asShow" title="还款计划">
      <div class="as-content">
        <div class="info">
          <div class="item">
            <span class="label">还款方式：</span>
            <strong>{{ stageDict.payType }}</strong>
          </div>
          <div class="item">
            <span class="label">贷款金额：</span>
            <span class="red">¥ {{ stgInfo.installmentAmount || 0}}</span>
          </div>
          <div class="item">
            <span class="label">总利息：</span>
            <span class="red">¥ {{ totalInterest }}</span><span v-if="subsidyTotal">(已贴息 ¥ {{ subsidyTotal }})</span>
          </div>
        </div>
        <table class="table">
          <thead>
            <th></th>
            <th>本金</th>
            <th>利息</th>
            <th>应还</th>
          </thead>
          <tbody>
            <tr v-for="(item,index) in paymentList" :key="index">
              <td>{{ index + 1 }}</td>
              <td>{{ parseFloat((item.principal)) }}</td>
              <td>{{ parseFloat((item.actualInterest)) }}</td>
              <td>{{ parseFloat((item.totalPayment)) }}</td>
            </tr>
            <tr>
              <td>总计</td>
              <td>{{ stgInfo.installmentAmount || 0}}</td>
              <td>{{ totalInterest || 0 }}</td>
              <td>{{ parseFloat((Number(stgInfo.installmentAmount) + Number(totalInterest))) }}</td>
            </tr>
          </tbody>
        </table>
      </div>
    </van-action-sheet>
    <van-popup class="pop" v-model="showPop"  round position="bottom" :style="{ height: '20%' }">
      <div class="untils flex1">
        <span @click="showPop = false">取消</span>
        <span @click="save">保存</span>
      </div>
      <van-field v-model="telPhone" autofocus  ref="getFocus" type="tel" label="修改手机号码" placeholder="请输入手机号"/>
    </van-popup>
    <div v-show="stgInfo.status === 3">
      <div class="pay-box">
        <h3>{{ stageDict.guideTitle }}</h3>
        <p class="tip-txt">{{ stageDict.guideDes }}</p>
        <div v-if="installmentType == 32" class="eqcode-box" @click="toApply">
          点击前往申请
        </div>
        <template v-else>
          <div v-if="imgData" class="qr-code-box">
            <img :src="imgData" alt="" />
          </div>
          <van-loading v-else size="64px" style="height: 1.9rem;line-height: 1.9rem;text-align: center;"/>
          <p style="color: red;text-align: center;">二维码3天内有效</p>
        </template>
        <div class="step-box">
          <span>步骤指引：</span>
          <p>① {{ stageDict.stepGuideFirst }}</p>
          <p>② {{ stageDict.stepGuideSecond }} </p>
          <p>③ 填写贷款资料与实名认证 </p>
          <p>④ 签署合同协议并完成支付 </p>
        </div>
        <div v-if="installmentType == 39" class="step-box">
          <span>注意：</span>
          <p>二维码3天内有效，后续查账还款，可以直接进入「海尔消费金融」公众号操作。若没有及时扫码，二维码失效后，需要取消订单，重新申请。</p>
        </div>
      </div>
    </div>

    <div class="loan-footer">
      <bottom-btns
          class="first-play"
          v-if="stgInfo.status === 1 && !showCancel"
          isOnlyOne="right"
          :rightBtnText='`立即支付首付${stgInfo.downPaymentAmount}元`'
          @rightClick='submit'
      />
      <bottom-btns
          v-if="stgInfo.status === 1 && showCancel"
          :leftBtnText='"取消订单"'
          :rightBtnText='`立即支付首付${stgInfo.downPaymentAmount}元`'
          isleftPlain
          @leftClick='handleCancel'
          @rightClick='submit'
      />

      <bottom-btns
          v-if="stgInfo.status === 2"
          :leftBtnText='"取消订单"'
          rightBtnText='申请额度'
          isleftPlain
          @leftClick='handleCancel'
          @rightClick='goStgPay'
      />
      <bottom-btns
          v-if="stgInfo.status === 3"
          :leftBtnText='"取消订单"'
          rightBtnText='查看指引教程'
          isleftPlain
          isRightPlain
          @leftClick='handleCancel'
          @rightClick='lookTips'
      />
    </div>
  </div>
</template>

<script>
import { Dialog, Toast } from 'vant';
import jrQrcode from 'jr-qrcode';
import yzSteps from "@/components/yz-steps";
import bottomBtns from "@/components/basic/bottom-btns";
import { calculateRepayment, calculateEqualInstallmentRate } from '@/utils/loanCalculator'
const stageDict = {
  // 学易
  xy: {
    step: '海尔学易分期贷款',
    name: '海尔学易分期',
    age: '18',
    earlyPay: '若提前还款，则需要承担剩余本金x3%的违约金。',
    payType: '等额本息',
    stepGuideFirst: '关注【学易分期】公众号',
    stepGuideSecond: '点击公众号内的卡片消息',
    guideTitle: '请点击下方按钮',
    guideDes: '前往海尔学易分期继续操作并完成支付',
    infoTip: '（仅支持修改手机号，其他信息请联系助学老师修改）'
  },
  // 消金
  xj: {
    step: '海尔消费金融贷款',
    name: '海尔消费金融',
    age: '20',
    earlyPay: '若提前还款，不需要承担违约金。',
    payType: '等本等息',
    stepGuideFirst: '扫码进入「海尔消费金融」公众号',
    stepGuideSecond: '点击公众号内的订单分期通知',
    guideTitle: '请长按图片识别二维码',
    guideDes: '前往海尔消费金额公众号继续操作',
    infoTip: '若需要修改个人信息，请联系助学老师修改'
  }
}
export default {
  components: { yzSteps, bottomBtns },
  data() {
    return {
      stageDict: {}, // 分期文案字典
      paymentList:[],
      isShowStep: [],
      topIndex:1, //步骤条索引
      // step:3,
      mobile:'',
      telPhone:'',
      qrCodeUrl:'',
      beforeInterestSum:0,//过去应还本金的累计
      totalInterest:0,//总利息
      stgInfo: {
        status:1,
        installmentConfig: {
          details: []
        }
      },
      stagingNum: void 0, // 分期数 6期：0.9%, 12期:0.9%
      stagingInterest:0.9,
      showCancel: false,
      asShow: false,
      showPop:false,
      installmentType: null, // 32: 海尔学易分期贷款 39: 海尔消费金融贷款
      imgData: '',
      subsidyProportion: 0, // 贴息比例
      subsidyTotal: 0, // 总贴息
    };
  },
  computed: {
    // 是否首付
    isDownPay() {
      //status: 1-初始化，2-已付首付，3-申请分期中，4-以申请额度，5-以放款

      return this.stgInfo.status >= 2;
    },

  },
  created() {
    this.handleStageDict();
  },
  mounted() {
    this.showCancel = this.$route.query.showCancel == 1;
    this.getOrderInfo();
  },
  methods:{
    handleStageDict() {
      this.installmentType = this.$route.query.installmentType;
      const stateName = this.installmentType == 32 ? 'xy' : 'xj';
      this.stageDict = stageDict[stateName];
      this.isShowStep = [ "支付首付", this.stageDict.step, "完成支付" ];
    },
    // 每一期应还
    everyRepayment(item){
      let amount =this.stagingInterest*(1+this.stagingNum*this.stagingInterest) / ((1+this.stagingInterest*this.stagingNum)-1)* this.stgInfo.installmentAmount;
      // console.log(amount,'amount3');
      return Number(amount.toFixed(2))
    },
    // 每一期利息
    everyTimeInterest() {
      let amount = ((this.stgInfo.installmentAmount-this.beforeInterestSum)*19.3802/100) / 360 * 30

      return Number(amount.toFixed(2))
    },
    // 每一期本金
    everyTimePrincipal() {
      let amount = this.everyRepayment() - this.everyTimeInterest();
      // this.beforeInterestSum += amount;
      return amount || 0
      // let amount = this.stgInfo.installmentAmount / this.stagingNum;
      // return Number(amount.toFixed(2))
    },
    handleCancel(){
      const isOrder = this.stgInfo.status === 1;
      Dialog.confirm({
        title: isOrder ? '确认要取消订单吗？' : '确认要取消申请吗？',
        message: isOrder ? '订单取消后, 你还可以选择其他支付方式' : '如果您未提交分期资料, 可以取消申请',
        confirmButtonColor: '#F06E6C',
        confirmButtonText: '确认取消',
        cancelButtonText: isOrder ? '再考虑一下' : '先等等',
      }).then(() => {
        this.cancelOrder();
      }).catch(() => {});
    },
    // 去分期
    async goStgPay() {
      if(!this.stagingNum){
        Toast('请选择贷款期数');
        return;
      }
      this.stgInfo.status = 3;
      // 海尔消费金融才需要显示二维码
      this.getXjQrCode();
    },
    // 获取消金二维码
    async getXjQrCode() {
      const res =  await this.toInstallmentPay();
      if (res.code === '00') {
        this.imgData = jrQrcode.getQrBase64(this.qrCodeUrl, { padding: 0 });
      }else {
        Toast(res.message);
      }
    },
    async toInstallmentPay(){
      const { orderNo } = this.$route.query;
      let params = {
        orderNo: orderNo,
        selInstallmentNum: this.stagingNum
      }
      const res =  await this.$http.post('/bds/toInstallmentPay/1.0/', params);
      if (res.code === '00') {
        this.qrCodeUrl = res.body.toUrl;
        //获取二维码
        //   let qrcode = new QRCode("qrcode", {
        //     width: 198,
        //     height: 198,
        //     text: eqUrl, // 二维码地址
        //     colorDark: "#000",
        //     colorLight: "#fff",
        // });
      }
      return res;
    },
    // 提交首付订单
    submit() {
      const { learnId, orderNo, coupons, accDeduction, zhimi, stdId, installmentType } = this.$route.query;
      let params = {
        learnId: learnId,
        orderNo: orderNo,
        stdId: stdId,
        coupons: coupons || '',
        demurrageScale: accDeduction || 0,
        zmScale: zhimi || 0,
        installmentType
      }

      this.$http.post('/bds/initOrderInstallment/1.0/', params)
        .then(res => {
          if (res.code === '00') {

            // 跳转到支付页
            this.$router.push({
              name: 'stuPayment',
              query: {
                learnId: learnId,
                isStgPay: 1,
                stgOrderNo: orderNo,
                installmentType
              },
            });
          }
        });
    },
    // 取消订单
    async cancelOrder() {
      const { orderNo } = this.$route.query;
      let params = {
        orderNo: orderNo
      }
      const res = await this.$http.post('/bds/installmentCancel/1.0/', params);
      if (res.code === '00') {
        Toast.success('取消成功');
        setTimeout(() => {
          this.$router.replace('/student');
        }, 200);
      }
    },
    // 获取订单信息
    getOrderInfo() {
      const { learnId, orderNo, coupons, accDeduction, zhimi, installmentType } = this.$route.query;
      let params = {
        learnId: learnId,
        orderNo: orderNo,
        coupons: coupons || '',
        demurrageScale: accDeduction || 0,
        zmScale: zhimi || 0,
        installmentType
      };
      this.$http.post('/bds/getOrderInstallment/1.0/', params)
        .then(res => {
          let { code, body } = res;
          if (code === '00') {
            this.stgInfo = body
            const detailsData = body?.installmentConfig?.details || []
            const findItem = detailsData.find((item) => item.period === body?.selInstallmentNum)
            if(findItem){
              this.selectCycle(findItem)
            }
            this.mobile = body.mobile;
            if (this.stgInfo.status === 3 && this.installmentType == 39) {
              this.stagingNum =  body.selInstallmentNum;
              this.getXjQrCode()
            }

            if(this.stgInfo.status >= 2){
              this.topIndex = 2
            }
          } else {
            setTimeout(() => {
              this.$router.go(-1);
            },2500)
          }
        })
    },
    editTel(){
      this.showPop = true;
    },
    lookTips(){
      if (this.installmentType == 32) {
        // 学易
        window.location.href = 'https://zm.yzou.cn/scholarshipStory/visitorScholarshipStoryInfo?scholarshipId=164664440694733804'
      }else if (this.installmentType == 39) {
        // 消金
        window.location.href = 'https://zm.yzou.cn/scholarshipStory/visitorScholarshipStoryInfo?scholarshipId=170246127684178736'
      }
    },
    async save(){
      let params = {
        id: this.stgInfo.id,
        mobile: this.telPhone
      }
      if(!this.telPhone){
        Toast('手机号不能为空！');
      }
      const res = await this.$http.post('/bds/updateOrderInstallmentInfo/2.0/', params);
      if (res.code === '00') {
        Toast('修改成功！');
        this.mobile = this.telPhone;
        this.showPop = false;
      }

    },
    selectCycle(obj) {
      const { period, subsidyProportion } = obj
      let actualInterestRate = obj?.interestRate
      this.stagingNum = Number(period)
      this.stagingInterest = Number(((actualInterestRate / period) * 100).toFixed(2))
      this.subsidyProportion = Number((subsidyProportion * 100).toFixed(2))
      // 计算总利息
      this.subsidyTotal = 0
      this.paymentList = []
      const { installmentAmount, installmentConfig } = this.stgInfo
      let RepaymentMethod = installmentConfig.repaymentType === 'EQUAL_PRINCIPAL_AND_EQUAL_INTEREST' ? 'EQUAL_PRINCIPAL' : 'EQUAL_PRINCIPAL_INTEREST'
      // 学易的特殊处理下，贴息改为等本等息
      if (this.subsidyProportion == 100) {
        RepaymentMethod = 'EQUAL_PRINCIPAL'
      }
      //实际年化利率
      if (RepaymentMethod === 'EQUAL_PRINCIPAL_INTEREST') {
        actualInterestRate = calculateEqualInstallmentRate(period, (installmentAmount * (1 + actualInterestRate)) / period, installmentAmount) * period
      }
      const { totalActualInterest, totalSubsidy, details } = calculateRepayment(installmentAmount, actualInterestRate * 100, period, RepaymentMethod, this.subsidyProportion, obj?.interestRate * 100)
      // 展示还款计划
      this.totalInterest = totalActualInterest
      this.subsidyTotal = totalSubsidy
      this.paymentList = details
    },    // 点击申请贷款
    async toApply(){
      const res =  await this.toInstallmentPay();
      if(res.code === '00'){
        window.location.href = this.qrCodeUrl;
      }else{
        Toast(res.message);
      }
    }
  }
};
</script>

<style lang = "less" scoped>
@import "../../../assets/less/variable.less";
/* * { touch-action: pan-y; } */
.loan-footer /deep/ .complete-bottom-btns button:nth-child(1){
  border-color: #A29B9BFF;
  font-weight: 400;
  color: #746A6AFF;
  flex: 1.5;
}

.loan-footer /deep/ .first-play button:nth-child(1){
  color: #FFFFFF;
}
.loan-footer /deep/ .complete-bottom-btns button:nth-child(2){
  flex: auto;
}
.pop /deep/ .van-field__label{
  width: .1.2rem;
}
.pop /deep/ .van-field__label{
  width: 8.2em;
}
.pop /deep/ .van-field__label span{
  color: #453838FF;
}
.flex1{
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.title{
  margin-bottom: .1rem;
  font-size: 0.15rem;
  font-weight: 600;
  line-height: 0.21rem;
}
.tips{
  font-size: 0.12rem;
  color: #7D7D7D;
  line-height: 0.17rem;
}
.mb10{
  margin-bottom: 10px;
}
.main-wrap {
  margin-bottom: .5rem;
  .yz-steps {
    padding: 0.25rem 0.1rem 0.3rem;
    width: 100%;
    height: .85rem;
    display: flex;
    /* margin-left: -.1rem; */
    /deep/.part {
      width: 0.5rem;
      flex: 1;
    }
  }
  .loan-cycle-box{
    margin: .15rem auto;
    padding: .12rem .15rem;
    width: 3.35rem;
    background: #FFFFFF;
    box-shadow: 0rem 0.02rem 0.08rem 0rem rgba(0, 0, 0, 0.1);
    border-radius: 0.05rem;

    .loan-time{
      display: flex;
      justify-content: space-between;
      .loan-time-box,.select{
        width: 1.38rem;
        height: 0.42rem;
        line-height: 0.42rem;
        text-align: center;
        font-size: 0.2rem;
        font-weight: 400;
        color: #999999;
        border-radius: 0.05rem;
        border: 0.01rem solid #CDCDCD;
      }
      .select{
        position: relative;
        background: rgba(240, 110, 108, 0.1);
        border-radius: 0.05rem;
        color: #F06E6C;
        border: 0.01rem solid #F06E6C;
        &::after{
          position: absolute;
          content: "";
          display: block;
          width: 0.25rem;
          height: 0.25rem;
          bottom: -0.01rem;
          right: -0.01rem;
          background: url('../../../assets/image/student/select.png') no-repeat;
          background-size: 100% 100%;
        }
      }
    }

    .interest-rate-box,.interest-plan{
      padding: .22rem 0 .13rem;
      border-bottom: 0.01rem solid #EBEBEB;
      p{
        font-size: 0.14rem;
        color: #453838;
      }
      .rate-between {
        flex: 1;
        display: flex;
        justify-content: space-between;
        padding-bottom: 0.1rem;
      }

      .placeholder {
        color: #CDCCCC;
      }

      .tip {
        color: red;
      }

      .interest-rate-txt{
        color: #000;
      }
    }
    .interest-plan{
      border: none;
      align-items: center;
      .interest-plan-r p{
        text-align: right;
        font-size: 0.12rem;
        color: #4AA3F2;
        line-height: 0.17rem;
      }
      img{
        width: 0.32rem;
        height: 0.32rem;
      }
    }

  }
  .user-info{
    margin: 0 auto;
    width: 3.35rem;
    padding: .16rem .15rem;
    background: #FFFFFF;
    box-shadow: 0rem 0.02rem 0.08rem 0rem rgba(0, 0, 0, 0.1);
    border-radius: 0.05rem;
    .user-info-head{
      margin-bottom: .1rem;
      border-bottom: 0.01rem solid #EBEBEB;
      span{
        color: #F06E6CFF;
        margin-right: .05rem;
      }
      img{
        width: 0.14rem;
        height: 0.14rem;
      }
    }
    .user-info-connect{
      .info-item{
        margin-bottom: .08rem;
        span{
          font-size: 0.14rem;
          color: #666666FF;
          line-height: 0.2rem;
          &:nth-child(2){
            color: #453838FF;
          }
        }
      }
    }
  }
  .pay-box{
    margin: .15rem auto 0;
    padding: .36rem .4rem .27rem;
    width: 3.35rem;
    // height: 3.8rem;
    background: #FFFFFF;
    box-shadow: 0rem 0.02rem 0.08rem 0rem rgba(0, 0, 0, 0.1);
    border-radius: 0.05rem;
    h3{
      text-align: center;
      font-size: 0.18rem;
      font-weight: 600;
      color: #000000;
      line-height: 0.25rem;
      margin-bottom: .1rem;
    }
    .tip-txt{
      text-align: center;
      font-size: 0.14rem;
      color: #999999;
      line-height: 0.2rem;
    }
    .qr-code-box {
      margin-top: 0.1rem;
      width: 100%;
      height: 1.8rem;
      text-align: center;
      img {
        width: 1.8rem;
        height: 1.8rem;
      }
    }
    .step-box{
      span{
        margin-bottom: .1rem;
        padding-top: .1rem;
        display: block;
        color: #999999;
      }
      p{
        margin-bottom: .05rem;
        font-size: 0.14rem;
        color: #333333;
      }
    }
    .eqcode-box{
      margin: .24rem auto;
      text-align: center;
      width: 2.35rem;
      height: 0.65rem;
      line-height: .65rem;
      background: linear-gradient(180deg, #EF8371 0%, #DB4C3E 100%);
      border-radius: 0.05rem;
      font-size: 0.18rem;
      font-weight: 600;
      color: #FFFFFF;
    }

  }
.cell {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #453838;
  font-size: 14px;
  .sub-cell {
    /* flex: 1; */
  }
}
.shadow-block {
  width: 3.35rem;
  margin: 0 .2rem;
  background: #FFFFFF;
  box-shadow: 0 .02rem .1rem 0 rgba(0, 0, 0, 0.1);
  border-radius: .04rem;

  .block-title {
    font-size: .15rem;
    font-weight: 600;
    color: #000000;
    padding-bottom: .1rem;
    border-bottom: 1px solid #F8F8F8;
  }

  .h2 {
    font-size: .15rem;
    font-weight: 600;
    color: #000000;
  }

  .rema {
    margin-top: .05rem;
    font-size: .12rem;
    font-weight: 400;
    color: #7D7D7D;
  }

}
  .question {
    margin-top: .15rem;
    margin-bottom: .8rem;
    .item {
      margin-top: .1rem;
    }

    .topic {
      font-size: .14rem;
      font-weight: 400;
      color: #000000;
    }

    .answer {
      font-size: .13rem;
      color: #7D7D7D;
    }
  }
  .p15 {
    padding: .15rem;
  }
}
.pop{
  padding: .2rem 0 0rem;
  .untils{
    padding: 0 .16rem;
  }
  .untils{
    margin-bottom: .1rem;
    span:nth-child(2){
      color: red;
    }
  }
}
  .as-content {
    padding: 0 .2rem .3rem .2rem;

    .info {
      .item {
        margin-top: .08rem;
        font-size: .14rem;
        .label {
          width: .8rem;
          margin-right: .2rem;
          display: inline-block;
          color: #9B9595;
        }
      }
    }

    .table {
      width: 3.35rem;
      margin-top: .15rem;

      th,td {
        border:1px solid #EBEBEB;
        text-align: center;
        padding: .06rem 0;
      }
    }
  }


</style>

