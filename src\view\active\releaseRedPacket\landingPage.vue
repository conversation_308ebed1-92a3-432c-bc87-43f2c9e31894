<template>
  <div class="landingPage-wrapper">
    <wx-open-launch-app id="launch-btn" @error="callAppError" :appid="OpenAppId">
      <script type="text/wxtag-template">
        <style>
    .down{
        width: 1000px;
        height: 1000px;
      z-index: 3;
    }
  </style>
  <div class='down'></div>
      </script>
    </wx-open-launch-app>
  </div>
</template>

<script>
import { isIOS, downloadApp } from '@/common'
import openApp from '@/mixins/openApp'
export default {
  data() {
    return {
      extinfo: 'http://pre-app.yzwill.cn/active/newYear20221230' //跳转所需额外信息
    }
  },
  mixins: [openApp],
  beforeRouteEnter(to, from, next) {
    //修复iOS版微信HTML5 History兼容性问题
    if (isIOS() && to.path !== location.pathname) {
      location.assign(to.fullPath)
    } else {
      next()
    }
  },
  created() {
    this.$yzStatistic(
      'applet.customerServiceCard.click',
      '2',
      '小程序客服-点击卡片'
    )
  },
  mounted() {
    this.wxCallAppInit() // 微信标签唤起app
  },
  methods: {
    down() {
      downloadApp(true)
      this.$yzStatistic(
        'applet.customerServiceDownloadApp.event',
        '2',
        '小程序客服-下载app'
      )
    }
  }
}
</script>
<style lang="less" scoped>
.landingPage-wrapper {
  width: 100vw;
  height: 100vh;
  background: url('./img/兑换礼品-引导下载APP.png') no-repeat center;
  background-size: 100%;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  overflow: hidden;
}
</style>
