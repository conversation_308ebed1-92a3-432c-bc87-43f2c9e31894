<template>
  <!-- 26级城建专属弹窗 -->
  <div class="ucEnrollmen" :style="{ backgroundImage: isMineLink || `url(${require('@/assets/image/618invite/enrollmentbg.png')})` }">
    <!-- 邀请人点自己 -->
    <div v-if="isMineLink" class="ucEnrollmen-img">
      <img src="@/assets/image/618invite/bgse.png" alt="" srcset="" />
      <p>自己不能和自己成为同桌哦～</p>
    </div>
    <!-- 访客点邀请人 -->
    <div v-else class="ucEnrollmen-main">
      <img class="pmain-head" :src="inviteInfor.headImg" alt="" />
      <div class="pmain-text">Hi，【{{ inviteInfor.userName }}】正在远智教育提升学历，希望你能成为他的同桌，享奖学金优惠～</div>
      <div class="umain-amni">
        <img class="pmain-btn" @click="onBindInvitation" src="@/assets/image/618invite/main-btn.png" alt="" />
        <img class="pmain-hand" src="@/assets/image/618invite/btns-active.png" alt="" />
      </div>
      <div class="umain-desc">*点击按钮立即与好友成为同桌仅可与一名好友成为同桌</div>
    </div>
    <!-- 弹窗：点击绑定按钮 或 已经有绑定关系 -->
    <van-popup round v-model="showPopups" class="ucEnrollmen-popup">
      <div class="popup-main">
        <img class="pmain-bg" src="@/assets/image/618invite/popupbg.png" alt="" />
        <img class="pmain-head" :src="inviteInfor.headImg" alt="" />
        <div class="pmain-text">
          {{
            isBinded
              ? `您已经和【${inviteInfor.userName}】成为同桌了哦，如有疑问可咨询助学老师～`
              : `恭喜你即将和【${inviteInfor.userName}】成为同桌！点击“去报读”与好友一起提升学历吧~`
          }}
        </div>
        <img class="pmain-btn" :src="require(`@/assets/image/618invite/popup-${isBinded ? 'konw' : 'bind'}.png`)" @click="goH5Home(1)" alt="" />
        <div class="pmain-desc">（仅可与一名好友成为同桌）</div>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { isLogin, toLogin } from '@/common'
import { formatTimeByMoment } from '@/filters'
import { Toast } from 'vant'
import { setRawCookie, getRawCookie } from 'tiny-cookie'

export default {
  data() {
    return {
      inviteUserId: '', // 邀请人id
      customerId: '', // 登陆者用户id
      isLogin: isLogin(),
      isAppOpen: false,
      showPopups: false, // 弹窗
      isBinded: false, // 默认没有绑定
      isMineLink: false, // 是否是我自己点击的链接
      inviteInfor: {
        userName: '',
        invitationUserId: '',
        headImg: require('@/assets/image/618invite/record-no.png')
      }
    }
  },
  created() {
    this.$sensors.track('show', {
      event_title: '活动页-好友弹窗-曝光',
      event_path: '/settings/sendStamps/UrbanConstructionEnrollment',
      event_time: formatTimeByMoment(new Date())
    })
    console.clear()
    console.log('created-query', this.$route.query)
    this.inviteUserId = this.$route.query?.inviteUserId || ''
    if (!this.inviteUserId) {
      Toast({ message: '邀约参数不可为空', duration: 2000 })
      return
    }
    this.getSecodeIntInfor()
    // 已登录，则获取有效邀约数据
    if (this.isLogin) {
      this.customerId = this.storage.getItem('userId') || ''
      // 没有userid，则先获取userid
      if (!this.customerId) {
        this.$http.post('/mkt/getUserIdByYzCode/1.0/', { yzCode: this.storage.getItem('yzCode') }).then((res) => {
          if (res?.body) {
            this.customerId = res.body || ''
            this.storage.setItem('userId', res.body)
            this.getAll()
          } else {
            Toast({ message: '获取有效邀约数据错误', duration: 2000 })
          }
        })
      } else this.getAll() // 否则，直接判断
    }
    // 未登录，则默认展示query邀约人信息
    else {
      setRawCookie('ucEnrollment', 1)
    }
  },
  methods: {
    // 判断邀约人和用户是否是同一个
    getAll() {
      if (this.inviteUserId == this.customerId) {
        this.isMineLink = true
        return
      }
      // 不是
      setTimeout(() => {
        this.getMyEffectiveInvite()
      }, 300)
    },
    // 获取邀请人信息
    getSecodeIntInfor() {
      const inviteId = this.$route.query?.inviteId || ''
      if (!inviteId) {
        Toast({ message: '邀请人token不可为空', duration: 2000 })
        return
      }
      const token = decodeURIComponent(inviteId).replace(/ /g, '+')
      this.$http.post('/us/getInviteInfo/1.0/', { inviteToken: token }).then((res) => {
        const { body } = res
        console.log('获取邀请人信息-body', body)
        this.inviteInfor = {
          userName: body?.realName || '',
          invitationUserId: body?.userId || '',
          headImg: body?.headImg || this.inviteInfor.headImg
        }
      })
    },
    // 获取是否有绑定关系
    getMyEffectiveInvite() {
      this.$http
        .post('/mkt/getValidInvitation/1.0/', {
          activityId: '238',
          invitationUserId: this.inviteUserId,
          customerUserId: this.customerId
        })
        .then((res) => {
          const { code, body } = res
          // 是有效邀约人（已绑定过），展示并且跳转
          console.log('获取是否有绑定关系-body', body)
          if (code === '00' && body?.invitationUserId) {
            this.inviteInfor = {
              userName: body?.userName || '',
              invitationUserId: body?.invitationUserId || '',
              headImg: body?.headImg || this.inviteInfor.headImg
            }
            this.isBinded = true
            this.showPopups = true
            this.goH5Home()
          } else {
            /**
             * @desc 判断是否是从【当前页】前往【登录页】的
             * @param ucEnrollment
             * 0/null 默认值
             * 1 标记由【当前页】跳转到【登录页】
             * 2 表示是在【1】的基础之上，从【登录页】成功登录
             */
            console.log('判断是否是从当前页面前往登录页的', getRawCookie('ucEnrollment'))
            if (getRawCookie('ucEnrollment') == 2) {
              this.onBindInvitation()
            }
          }
        })
    },
    // 绑定有效邀请关系
    onBindInvitation() {
      if (!this.inviteUserId) {
        console.log('邀约参数不可为空')
        Toast({ message: '邀约参数不可为空', duration: 2000 })
        return
      }
      // 判断登录，未登录，则前往登录页面
      if (!this.isLogin) {
        console.log('未登录，则前往登录页面')
        if (!this.isAppOpen) {
          toLogin.call(this, null)
          return
        }
      }
      // 被调用时绑定关系
      this.$sensors.track('click', {
        event_title: '活动页-好友弹裔-【与好友成为同桌】点击',
        event_path: '/settings/sendStamps/UrbanConstructionEnrollment',
        event_time: formatTimeByMoment(new Date())
      })
      this.$http
        .post('/mkt/createInvitation/1.0/', {
          activityId: '238',
          invitationUserId: this.inviteUserId,
          customerUserId: this.customerId
        })
        .then((res) => {
          console.log('绑定有效邀请关系-成功：', res)
          if (res?.code === '00' && res?.body) {
            this.isBinded = false
            this.showPopups = true
            this.goH5Home()
            setRawCookie('ucEnrollment', 0)
          }
        })
    },
    // 前往大专首页
    goH5Home(type = 0) {
      this.$sensors.track('click', {
        event_title: `活动页-好友弹裔-【${this.isBinded ? '我知道了' : '去报读学历'}】点击`,
        event_path: '/settings/sendStamps/UrbanConstructionEnrollment',
        event_time: formatTimeByMoment(new Date())
      })
      // 助力成功后，前往大专页面
      setTimeout(
        () => {
          this.$router.push({
            name: 'secondMain',
            query: { ...this.$route.query, pfsnLevel: 5 }
          })
        },
        type ? 0 : 4000
      )
    }
  }
}
</script>

<style lang="less" scoped>
.ucEnrollmen {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  .ucEnrollmen-img {
    text-align: center;
    img {
      width: 1.6rem;
      height: 1.6rem;
    }
    p {
      height: 0.22rem;
      font-weight: 400;
      font-size: 0.16rem;
      color: #333333;
      line-height: 0.22rem;
    }
  }
  .ucEnrollmen-main {
    margin-top: 0;
    background-image: url(@/assets/image/618invite/enrollment-secbg.png);
    background-size: 100% 96%;
    background-repeat: no-repeat;
    background-position: 0 0.17rem;
    .pmain-text {
      font-weight: 550;
    }
    .umain-amni {
      position: relative;
      animation: centerAmplify 1s infinite;
      .pmain-btn {
        margin-top: 0.9;
      }
      .pmain-hand {
        width: 0.58rem;
        height: 0.58rem;
        position: absolute;
        top: 0.96rem;
        right: 0.3rem;
      }
    }
    .umain-desc {
      margin: auto;
      width: 1.7rem;
      height: 0.24rem;
      font-weight: 400;
      font-size: 0.12rem;
      color: #666666;
      line-height: 0.24rem;
    }
    // 动画 - 中心放大
    @keyframes centerAmplify {
      0% {
        transform: scale(0.8);
      }

      20% {
        transform: scale(0.9);
      }

      40% {
        transform: scale(1);
      }

      55% {
        transform: scale(1.1);
      }

      60% {
        transform: scale(1);
      }

      80% {
        transform: scale(0.9);
      }

      100% {
        transform: scale(0.8);
      }
    }
  }
}
.ucEnrollmen-popup {
  height: 100vh;
  overflow: hidden;
  overflow-y: scroll;
  text-align: center;
  background-color: rgba(0, 0, 0, 0);
  .popup-main {
    margin-top: 1rem;
    position: relative;
  }
  .popup-close {
    width: 0.32rem;
    height: 0.32rem;
    margin-top: 0.3rem;
  }
}
.ucEnrollmen-main,
.popup-main {
  width: 3.4rem;
  height: 3.8rem;
  overflow: hidden;
  text-align: center;
  .pmain-bg {
    width: 3.4rem;
    height: 3.1rem;
    position: absolute;
    top: 0.2rem;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: -1;
  }
  .pmain-head {
    width: 0.7rem;
    height: 0.7rem;
    box-shadow: 0 0.03rem 0.04rem 0.01rem rgba(164, 50, 11, 0.5);
    border: 0.02rem solid #ffe3b5;
    border-radius: 50%;
    background-color: #fff;
  }
  .pmain-text {
    width: 2.6rem;
    height: 0.48rem;
    margin: 0.2rem auto 0;
    font-weight: 400;
    font-size: 0.18rem;
    color: #cc2725;
    line-height: 0.24rem;
    text-align: center;
  }
  .pmain-btn {
    width: 2.4rem;
    height: 0.48rem;
    margin: 0.8rem auto 0.2rem;
  }
  .pmain-desc {
    height: 0.24rem;
    font-weight: 400;
    font-size: 0.12rem;
    color: #666666;
    line-height: 0.24rem;
  }
}
</style>
