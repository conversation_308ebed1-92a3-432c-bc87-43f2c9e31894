<template>
  <div class="main-content" v-html="rulesContent"></div>
</template>

<script>
export default {
  name: "Rule",
  props: {
    rulesContent: {
      type: String,
      required: true,
      default: ''
    }
  }
}
</script>

<style lang="less" scoped>
.main-content {
  padding: 0.15rem 0.2rem;
  /deep/ h1 {
    text-align: center;
    font-weight: 600;
    font-size: 0.16rem;
    color: #453838;
  }
  /deep/ h3 {
    font-weight: 600;
    font-size: 0.14rem;
    color: #453838;
  }
  /deep/ h4 {
    font-weight: 600;
    font-size: 0.14rem;
    color: #453838;
  }
  /deep/ p {
    font-size: 0.14rem;
    color: #453838;
    line-height: 0.22rem;
  }
  .rule-box {
   /deep/ h1 {
      text-align: center;
      font-weight: 600;
      font-size: 0.14rem;
      color: #453838;
    }
   /deep/ h3 {
      font-weight: 600;
      font-size: 0.14rem;
      color: #453838;
    }
   /deep/ h4 {
      font-weight: 600;
      font-size: 0.14rem;
      color: #453838;
    }
   /deep/ p {
      font-size: 0.14rem;
      color: #453838;
      line-height: 0.22rem;
    }
  }
  .all {
    text-align: center;
    font-size: 0.14rem;
    color: #453838;
    line-height: 0.22rem;
  }
}
</style>
